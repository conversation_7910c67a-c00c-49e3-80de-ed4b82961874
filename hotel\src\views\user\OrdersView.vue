<template>
  <div class="orders-page">
    <AppNavbar />

    <!-- 主要内容 -->
    <main class="orders-main">
      <div class="orders-container">
        <div class="page-header">
          <h1 class="page-title">我的预订</h1>
          <p class="page-subtitle">管理您的庄园住宿预订</p>
        </div>

        <!-- 订单状态筛选 -->
        <div class="order-filters">
          <button 
            v-for="status in orderStatuses" 
            :key="status.value"
            @click="currentFilter = status.value"
            :class="['filter-btn', { active: currentFilter === status.value }]"
          >
            {{ status.label }}
            <span class="count" v-if="getOrderCount(status.value) > 0">
              {{ getOrderCount(status.value) }}
            </span>
          </button>
        </div>

        <!-- 订单列表 -->
        <div class="orders-section">
          <!-- 未登录提示 -->
          <div v-if="!auth.isAuthenticated" class="login-prompt">
            <i class="fas fa-user-lock"></i>
            <h3>请先登录</h3>
            <p>登录后即可查看您的订单信息</p>
            <div class="prompt-actions">
              <router-link to="/login" class="btn btn-primary">立即登录</router-link>
              <router-link to="/register" class="btn btn-outline">注册账号</router-link>
            </div>
          </div>

          <!-- 已登录用户的订单内容 -->
          <template v-else>
            <div v-if="loading" class="loading">
              <i class="fas fa-spinner fa-spin"></i>
              <span>加载中...</span>
            </div>

            <div v-else-if="filteredOrders.length === 0" class="no-orders">
              <i class="fas fa-calendar-times"></i>
              <h3>暂无订单</h3>
              <p>您还没有任何{{ currentFilterLabel }}订单</p>
              <router-link to="/ai-rooms" class="btn btn-primary">去预订民宿</router-link>
            </div>

            <div v-else class="orders-list">
            <div 
              v-for="order in filteredOrders" 
              :key="order.id" 
              class="order-card"
            >
              <div class="order-header">
                <div class="order-info">
                  <h3 class="order-number">订单号：{{ order.orderNo || order.orderNumber }}</h3>
                  <span class="order-date">下单时间：{{ formatDate(order.createdAt || order.createTime) }}</span>
                  <span class="order-type">{{ order.type === 'product' ? '产品订单' : '房间订单' }}</span>
                </div>
                <div class="order-status" :class="order.status">
                  {{ getStatusText(order.status) }}
                </div>
              </div>
              
              <div class="order-content">
                <!-- 房间订单内容 -->
                <div v-if="order.type !== 'product'" class="room-info">
                  <img :src="order.roomImage || '/src/assets/images/实地调研/房间参观/房间参观1.jpg'" :alt="order.roomName" class="room-image">
                  <div class="room-details">
                    <h4 class="room-name">{{ order.roomName }}</h4>
                    <p class="room-type">{{ order.roomType }}</p>
                    <div class="stay-info">
                      <span class="check-in">入住：{{ formatDate(order.checkInDate) }}</span>
                      <span class="check-out">退房：{{ formatDate(order.checkOutDate) }}</span>
                      <span class="nights">共{{ order.nights }}晚</span>
                    </div>
                  </div>
                </div>

                <!-- 产品订单内容 -->
                <div v-if="order.type === 'product'" class="product-info">
                  <div v-for="item in order.items" :key="item.id" class="product-item">
                    <img :src="item.image" :alt="item.name" class="product-image">
                    <div class="product-details">
                      <h4 class="product-name">{{ item.name }}</h4>
                      <p class="product-desc">{{ item.description }}</p>
                      <div class="product-specs" v-if="item.specs">
                        <span v-for="spec in item.specs.slice(0, 2)" :key="spec" class="spec-tag">{{ spec }}</span>
                      </div>
                    </div>
                    <div class="product-quantity">
                      <span>x{{ item.quantity }}</span>
                    </div>
                    <div class="product-price">
                      <span class="current-price">¥{{ (item.price * item.quantity).toFixed(2) }}</span>
                    </div>
                  </div>

                  <!-- 收货信息 -->
                  <div class="shipping-info">
                    <div class="info-item">
                      <span class="label">收货地址：</span>
                      <span class="value">{{ order.shippingAddress }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">联系电话：</span>
                      <span class="value">{{ order.contactPhone }}</span>
                    </div>
                  </div>
                </div>
                
                <div class="order-price">
                  <div class="price-breakdown">
                    <div class="price-item">
                      <span>房费：</span>
                      <span>¥{{ order.roomPrice }} × {{ order.nights }}晚</span>
                    </div>
                    <div class="price-item total">
                      <span>总计：</span>
                      <span class="total-amount">¥{{ order.totalAmount }}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="order-actions">
                <button 
                  v-if="order.status === 'pending'"
                  @click="cancelOrder(order)"
                  class="btn btn-outline btn-danger"
                >
                  取消订单
                </button>
                
                <button 
                  v-if="order.status === 'confirmed'"
                  @click="viewOrderDetails(order)"
                  class="btn btn-outline"
                >
                  查看详情
                </button>
                
                <button 
                  v-if="order.status === 'completed'"
                  @click="rateOrder(order)"
                  class="btn btn-primary"
                >
                  评价订单
                </button>
                
                <button 
                  @click="viewOrderDetails(order)"
                  class="btn btn-outline"
                >
                  订单详情
                </button>
              </div>
            </div>
            </div>
          </template>
        </div>
      </div>
    </main>


  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useCartStore, type CartOrder } from '../../stores/cart'
import request from '../../utils/request'
import { productAI } from '../../services/ProductAIService'
import AppNavbar from '../../components/AppNavbar.vue'


const router = useRouter()
const route = useRoute()
const auth = useAuthStore()
const cartStore = useCartStore()

// 响应式数据
const loading = ref(false)
const orders = ref<any[]>([])
const currentFilter = ref('all')

// 房间图片映射
const roomImageMap: { [key: string]: string } = {
  '201': '/src/assets/images/实地调研/房间参观/房间参观1.jpg',
  '202': '/src/assets/images/实地调研/房间参观/房间参观1.jpg',
  '301': '/src/assets/images/实地调研/房间参观/房间参观2.jpg',
  '302': '/src/assets/images/实地调研/房间参观/房间参观2.jpg',
  '401': '/src/assets/images/实地调研/室内调研/室内3.jpg',
  '402': '/src/assets/images/实地调研/室内调研/室内3.jpg',
  '501': '/src/assets/images/实地调研/室内调研/室内5.jpg',
  '502': '/src/assets/images/实地调研/室内调研/室内5.jpg',
  '601': '/src/assets/images/实地调研/室内调研/室内7.jpg',
  '602': '/src/assets/images/实地调研/室内调研/室内7.jpg'
}

// 房间名称映射
const roomNameMap: { [key: string]: string } = {
  '201': '蘑菇森林小屋',
  '202': '蘑菇森林小屋',
  '301': '蘑菇生态园',
  '302': '蘑菇生态园',
  '401': '茶文化体验馆',
  '402': '茶文化体验馆',
  '501': '普洱茶园',
  '502': '普洱茶园',
  '601': '生态餐厅',
  '602': '生态餐厅'
}

// 订单状态配置
const orderStatuses = [
  { value: 'all', label: '全部订单' },
  { value: 'pending', label: '待确认' },
  { value: 'confirmed', label: '已确认' },
  { value: 'completed', label: '已完成' },
  { value: 'cancelled', label: '已取消' }
]

// 获取购物车订单
const cartOrders = computed(() => {
  return cartStore.getUserOrders().map(order => ({
    id: order.id,
    orderNo: order.id,
    status: order.status,
    totalAmount: order.totalAmount,
    createdAt: order.createdAt,
    type: 'product',
    items: order.items,
    shippingAddress: order.shippingAddress,
    contactPhone: order.contactPhone,
    remarks: order.remarks
  }))
})

// 合并所有订单
const allOrders = computed(() => {
  return [...orders.value, ...cartOrders.value].sort((a, b) =>
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  )
})

// 计算属性
const filteredOrders = computed(() => {
  if (currentFilter.value === 'all') {
    return allOrders.value
  }
  return allOrders.value.filter(order => order.status === currentFilter.value)
})

const currentFilterLabel = computed(() => {
  const status = orderStatuses.find(s => s.value === currentFilter.value)
  return status ? status.label.replace('订单', '') : ''
})

// 辅助函数
const calculateNights = (checkIn: string, checkOut: string) => {
  if (!checkIn || !checkOut) return 0
  const checkInDate = new Date(checkIn)
  const checkOutDate = new Date(checkOut)
  const diffTime = checkOutDate.getTime() - checkInDate.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays > 0 ? diffDays : 0
}

const mapOrderStatus = (state: string, status?: number) => {
  // 优先使用state字段，如果没有则使用status字段映射
  if (state) {
    const stateMap: Record<string, string> = {
      '预订': 'pending',    // 待确认/预订中
      '入住': 'confirmed',  // 已确认/入住中
      '完成': 'completed',  // 已完成
      '取消': 'cancelled',  // 已取消
      '删除': 'cancelled'   // 已删除（显示为取消）
    }
    return stateMap[state] || 'pending'
  }

  // 备用：根据后端status状态码映射到前端状态
  const statusMap: Record<string | number, string> = {
    0: 'pending',    // 正常 -> 待确认
    1: 'completed',  // 完成 -> 已完成
    2: 'cancelled',  // 关闭 -> 已取消
    3: 'cancelled',  // 删除 -> 已取消
    'pending': 'pending',
    'confirmed': 'confirmed',
    'completed': 'completed',
    'cancelled': 'cancelled'
  }
  return statusMap[status || 0] || 'pending'
}

// 方法
const fetchOrders = async () => {
  loading.value = true
  try {
    const response: any = await request.get('/h/order/getUserOrders')
    console.log('订单列表响应:', response)

    if (response.code === 200 || response.code === "200") {
      // 检查是否有data和records字段
      if (response.data && Array.isArray(response.data.records)) {
        orders.value = response.data.records.map((order: any) => {
          const roomCode = order.roomCode || order.roomName
          return {
            id: order.id,
            orderNumber: order.orderNumber || `MG${order.id.toString().padStart(8, '0')}`,
            roomName: roomNameMap[roomCode] || order.roomName || roomCode || '房间名称',
            roomType: order.roomType || '房间类型',
            roomImage: roomImageMap[roomCode] || order.roomImage || '/src/assets/images/实地调研/房间参观/房间参观1.jpg',
          checkInDate: order.startDate,
          checkOutDate: order.endDate,
          nights: order.days || calculateNights(order.startDate, order.endDate),
          roomPrice: order.roomPrice || order.price,
          totalAmount: order.total || order.amount,
          status: mapOrderStatus(order.state, order.status), // 传递state和status
          state: order.state, // 保留原始state字段
          systemStatus: order.status, // 保留原始status字段
          createTime: order.createTime,
          guestCount: order.amount,
          phone: order.phone,
          specialRequirements: order.userRemark
          }
        })
        console.log('处理后的订单列表:', orders.value)
      } else {
        // 没有records字段或records不是数组，设置为空数组
        orders.value = []
        console.log('没有订单数据，设置为空数组')
      }
    } else {
      throw new Error(response.msg || '获取订单列表失败')
    }
  } catch (error: any) {
    console.error('获取订单列表失败:', error)
    // 如果获取失败，显示空列表
    orders.value = []

    // 检查是否是登录问题
    if (error.response && error.response.status === 401) {
      console.log('401错误，跳转到登录页')
      alert('登录已过期，请重新登录')
      auth.logout()
      router.push('/login')
    } else if (error.response && error.response.status === 500) {
      const errorMessage = error.response.data?.message || error.message || ''
      console.log('500错误，错误信息:', errorMessage)
      if (errorMessage.includes('Token无效') || errorMessage.includes('NotLoginException')) {
        alert('登录已过期，请重新登录')
        auth.logout()
        router.push('/login')
      } else {
        alert('获取订单列表失败，请稍后重试')
      }
    } else if (error.message && error.message.includes('登录已过期')) {
      // 这是从request拦截器抛出的错误
      console.log('Token过期错误，跳转到登录页')
      auth.logout()
      router.push('/login')
    } else {
      console.log('其他错误:', error.message)
      alert('获取订单列表失败，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}

const getOrderCount = (status: string) => {
  if (status === 'all') return orders.value.length
  return orders.value.filter(order => order.status === status).length
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待确认',
    confirmed: '已确认',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const cancelOrder = async (order: any) => {
  // 检查订单状态 - 使用后端的state字段而不是前端映射的status
  console.log('订单信息:', order)
  console.log('订单state:', order.state)
  console.log('订单status:', order.status)

  // 检查后端的state字段，只有"预订"状态才能取消
  if (order.state !== '预订') {
    alert(`只有预订状态的订单才能取消，当前状态：${order.state || '未知'}`)
    return
  }

  if (confirm('确定要取消这个订单吗？')) {
    try {
      console.log('取消订单请求:', order.id)
      const response: any = await request.delete(`/h/order/${order.id}`)
      console.log('取消订单响应:', response)

      if (response.code === 200 || response.code === "200") {
        alert('订单已取消')
        // 重新获取订单列表
        fetchOrders()

        // 通知其他页面刷新房间状态
        window.dispatchEvent(new CustomEvent('orderCancelled', {
          detail: { roomId: order.roomId, orderId: order.id }
        }))
      } else {
        const errorMsg = response.msg || response.message || '取消订单失败'
        alert(errorMsg)
        console.error('取消订单失败:', response)
      }
    } catch (error: any) {
      console.error('取消订单失败:', error)
      let errorMsg = '取消订单失败，请稍后重试'

      if (error.response) {
        // 服务器返回了错误响应
        if (error.response.status === 404) {
          errorMsg = '订单不存在或已被删除'
        } else if (error.response.status === 403) {
          errorMsg = '没有权限取消此订单'
        } else if (error.response.data?.msg) {
          errorMsg = error.response.data.msg
        }
      }

      alert(errorMsg)
    }
  }
}

const viewOrderDetails = (order: any) => {
  router.push(`/order/${order.id}`)
}

const rateOrder = (order: any) => {
  router.push(`/order/${order.id}/rate`)
}

// handleLogout 现在由 AppNavbar 组件处理

// 处理产品订单创建
const handleProductOrder = () => {
  const action = route.query.action
  if (action === 'create') {
    const productId = route.query.productId as string
    const quantity = parseInt(route.query.quantity as string) || 1
    const items = route.query.items as string

    if (productId) {
      // 单个产品订单
      createProductOrder(productId, quantity)
    } else if (items) {
      // 购物车订单
      try {
        const cartItems = JSON.parse(items)
        createCartOrder(cartItems)
      } catch (error) {
        console.error('解析购物车数据失败:', error)
      }
    }
  }
}

// 创建产品订单
const createProductOrder = async (productId: string, quantity: number) => {
  const product = productAI.getProductById(productId)
  if (!product) {
    alert('产品不存在')
    return
  }

  const orderData = {
    type: 'product',
    productId: product.id,
    productName: product.name,
    quantity: quantity,
    unitPrice: product.price,
    totalAmount: product.price * quantity,
    status: 'pending',
    createTime: new Date().toISOString()
  }

  try {
    // 这里可以调用后端API创建订单
    // const response = await request.post('/h/order/createProductOrder', orderData)

    // 暂时模拟创建成功
    console.log('创建产品订单:', orderData)
    alert(`成功创建订单：${product.name} x ${quantity}`)

    // 清除URL参数
    router.replace('/orders')

    // 刷新订单列表
    fetchOrders()
  } catch (error) {
    console.error('创建产品订单失败:', error)
    alert('创建订单失败，请稍后重试')
  }
}

// 创建购物车订单
const createCartOrder = async (cartItems: any[]) => {
  const totalAmount = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)

  const orderData = {
    type: 'cart',
    items: cartItems,
    totalAmount: totalAmount,
    status: 'pending',
    createTime: new Date().toISOString()
  }

  try {
    // 这里可以调用后端API创建订单
    // const response = await request.post('/h/order/createCartOrder', orderData)

    // 暂时模拟创建成功
    console.log('创建购物车订单:', orderData)
    alert(`成功创建订单，共${cartItems.length}件商品`)

    // 清除URL参数
    router.replace('/orders')

    // 刷新订单列表
    fetchOrders()
  } catch (error) {
    console.error('创建购物车订单失败:', error)
    alert('创建订单失败，请稍后重试')
  }
}

// 生命周期
onMounted(() => {
  if (auth.isAuthenticated) {
    // 初始化购物车数据
    cartStore.initializeUserData()
    fetchOrders()
  }
  handleProductOrder()
})
</script>

<style scoped>
/* 页面容器 */
.orders-page {
  min-height: 100vh;
  background-color: #f8fafc;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 导航栏样式 */
.navbar {
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #d4af37;
}

.nav-menu {
  display: flex;
  gap: 32px;
}

.nav-link {
  text-decoration: none;
  color: #6b7280;
  font-weight: 500;
  transition: color 0.3s ease;
  padding: 8px 0;
  border-bottom: 2px solid transparent;
}

.nav-link:hover,
.nav-link.active {
  color: #d4af37;
  border-bottom-color: #d4af37;
}

.nav-buttons {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-welcome {
  color: #6b7280;
  font-weight: 500;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-outline {
  background: transparent;
  color: #d4af37;
  border: 1px solid #d4af37;
}

.btn-outline:hover {
  background: #d4af37;
  color: white;
}

.btn-primary {
  background: #d4af37;
  color: white;
  border: 1px solid #d4af37;
}

.btn-primary:hover {
  background: #b8941f;
  border-color: #b8941f;
}

.btn-danger {
  color: #dc2626;
  border-color: #dc2626;
}

.btn-danger:hover {
  background: #dc2626;
  color: white;
}

/* 主要内容 */
.orders-main {
  padding: 40px 0;
}

.orders-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 1.125rem;
  color: #6b7280;
}

/* 订单筛选器 */
.order-filters {
  display: flex;
  gap: 16px;
  margin-bottom: 32px;
  justify-content: center;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 12px 24px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-btn:hover {
  border-color: #d4af37;
  color: #d4af37;
}

.filter-btn.active {
  border-color: #d4af37;
  background: #d4af37;
  color: white;
}

.count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: bold;
}

.filter-btn.active .count {
  background: rgba(255, 255, 255, 0.3);
}

/* 登录提示 */
.login-prompt {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.login-prompt i {
  font-size: 3rem;
  margin-bottom: 16px;
  color: #d4af37;
}

.login-prompt h3 {
  color: #1f2937;
  margin-bottom: 8px;
  font-size: 1.5rem;
}

.login-prompt p {
  margin-bottom: 24px;
}

.prompt-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 加载和空状态 */
.loading {
  text-align: center;
  padding: 60px 0;
  color: #6b7280;
}

.loading i {
  font-size: 2rem;
  margin-bottom: 16px;
  display: block;
}

.no-orders {
  text-align: center;
  padding: 60px 0;
  color: #6b7280;
}

.no-orders i {
  font-size: 4rem;
  margin-bottom: 24px;
  display: block;
  color: #d1d5db;
}

.no-orders h3 {
  font-size: 1.5rem;
  margin-bottom: 8px;
  color: #374151;
}

.no-orders p {
  margin-bottom: 24px;
}

/* 订单列表 */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.order-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f3f4f6;
  background: #f9fafb;
}

.order-info h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.order-date {
  font-size: 0.875rem;
  color: #6b7280;
}

.order-status {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

.order-status.pending {
  background: #fef3c7;
  color: #d97706;
}

.order-status.confirmed {
  background: #d1fae5;
  color: #059669;
}

.order-status.completed {
  background: #dbeafe;
  color: #2563eb;
}

.order-status.cancelled {
  background: #fee2e2;
  color: #dc2626;
}

.order-content {
  padding: 24px;
  display: flex;
  gap: 24px;
}

.room-info {
  display: flex;
  gap: 16px;
  flex: 1;
}

.room-image {
  width: 120px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
}

.room-details {
  flex: 1;
}

.room-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.room-type {
  color: #6b7280;
  margin-bottom: 12px;
}

.stay-info {
  display: flex;
  gap: 16px;
  font-size: 0.875rem;
  color: #6b7280;
}

.order-price {
  min-width: 200px;
}

.price-breakdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.price-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: #6b7280;
}

.price-item.total {
  font-weight: 600;
  color: #1f2937;
  font-size: 1rem;
  padding-top: 8px;
  border-top: 1px solid #f3f4f6;
}

.total-amount {
  color: #d4af37;
  font-size: 1.25rem;
}

.order-actions {
  padding: 20px 24px;
  border-top: 1px solid #f3f4f6;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-content {
    flex-direction: column;
  }

  .room-info {
    flex-direction: column;
  }

  .room-image {
    width: 100%;
    height: 200px;
  }

  .stay-info {
    flex-direction: column;
    gap: 4px;
  }

  .order-actions {
    flex-direction: column;
  }

  .filter-btn {
    flex: 1;
    justify-content: center;
  }
}
</style>
