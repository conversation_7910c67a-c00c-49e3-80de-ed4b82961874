<template>
  <div class="ai-room-selection">
    <AppNavbar />
    
    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="container">
        <!-- 上下分层布局 -->
        <div class="layout-container">
          <!-- 上半部分：AI对话区域 -->
          <div class="top-section">
            <!-- 左侧区域 -->
            <div class="left-panel">
            <!-- 第一层：AI头像和名字 -->
            <div class="ai-header-section">
              <div class="ai-avatar">
                <div class="ai-character-container" @click="toggleAIVoice">
                  <img :src="aiImageUrl" alt="蘑菇小人" :class="['ai-character', aiState]">
                  <div class="ai-expression-overlay">
                    <div :class="['ai-expression', currentExpression]">{{ getExpressionEmoji() }}</div>
                  </div>
                  <div v-if="isSpeaking" class="ai-speaking-indicator">
                    <div class="sound-wave"></div>
                    <div class="sound-wave"></div>
                    <div class="sound-wave"></div>
                  </div>
                </div>
              </div>
              <div class="ai-intro">
                <h1 class="ai-title">🍄 AI选房助手</h1>
                <p class="ai-subtitle">{{ aiStatusText }}</p>
                <div class="ai-controls">
                  <button @click="toggleAIVoice" :class="['ai-voice-toggle', { active: voiceEnabled }]">
                    <i :class="voiceEnabled ? 'fas fa-volume-up' : 'fas fa-volume-mute'"></i>
                    {{ voiceEnabled ? '语音开启' : '语音关闭' }}
                  </button>
                </div>
              </div>
            </div>

            <!-- 第二层：AI推荐房间列表 -->
            <div class="ai-recommendations-section">
              <div class="section-header">
                <h3>🏠 符合条件的房间</h3>
                <p class="recommendation-subtitle">根据您的需求筛选出的可选房间，请与AI助手对话完成预订</p>
              </div>
              <div class="recommendations-list">
                <div v-if="aiRecommendations.length === 0" class="no-recommendations">
                  <div class="empty-state">
                    <i class="fas fa-home"></i>
                    <p>请与AI助手对话，告诉我您的需求</p>
                    <p class="empty-subtitle">我会为您筛选出符合条件的房间</p>
                  </div>
                </div>
                <div v-for="room in aiRecommendations" :key="room.id" class="recommendation-card">
                  <div class="room-image">
                    <img :src="room.image" :alt="room.name" />
                    <!-- 删除房间状态标签，保持图片简洁 -->
                  </div>
                  <div class="room-info">
                    <div class="room-header">
                      <h4 class="room-name">{{ room.name }}</h4>
                      <span class="room-price">¥{{ room.price }}/晚</span>
                    </div>
                    <div class="room-meta">
                      <span class="room-code">房间号：{{ room.code }}</span>
                      <span class="room-category">{{ room.category }}</span>
                      <span class="room-rating">⭐{{ room.rating }}</span>
                    </div>
                    <div class="room-status-info">
                      <span class="availability-status" :class="room.status">
                        {{ room.status === 'available' ? '✅ 可预订' : '❌ 已预订' }}
                      </span>
                      <!-- VR链接 -->
                      <a v-if="room.vrUrl" :href="room.vrUrl" target="_blank" class="vr-link">
                        VR看房
                      </a>
                    </div>
                    <!-- AI推荐区域保持简洁，不显示推荐原因 -->
                  </div>
                </div>
              </div>
            </div>

            <!-- 第三层：订单详情 -->
            <div class="order-details-section">
              <div class="section-header">
                <h3>📋 订单详情</h3>
              </div>
              <div class="order-content">
                <div v-if="!currentOrder" class="no-order">
                  <div class="empty-state">
                    <i class="fas fa-receipt"></i>
                    <p>暂无订单信息</p>
                  </div>
                </div>
                <div v-else class="order-info">
                  <div class="order-header">
                    <h4>订单号：{{ currentOrder.orderNumber }}</h4>
                    <span class="order-status" :class="currentOrder.status">
                      {{ getOrderStatusText(currentOrder.status) }}
                    </span>
                  </div>
                  <div class="order-details">
                    <div class="detail-item">
                      <label>房间信息：</label>
                      <span>{{ currentOrder.roomName }} ({{ currentOrder.roomCode }})</span>
                    </div>
                    <div class="detail-item">
                      <label>入住日期：</label>
                      <span>{{ currentOrder.checkInDate }}</span>
                    </div>
                    <div class="detail-item">
                      <label>退房日期：</label>
                      <span>{{ currentOrder.checkOutDate }}</span>
                    </div>
                    <div class="detail-item">
                      <label>住宿天数：</label>
                      <span>{{ currentOrder.nights }}晚</span>
                    </div>
                    <div class="detail-item">
                      <label>房间价格：</label>
                      <span>¥{{ currentOrder.roomPrice }}/晚</span>
                    </div>
                    <div class="detail-item total">
                      <label>总金额：</label>
                      <span class="total-price">¥{{ currentOrder.totalAmount }}</span>
                    </div>
                    <div class="order-actions">
                      <button v-if="currentOrder.status === 'pending'" class="cancel-btn" @click="cancelOrder">
                        取消订单
                      </button>
                      <button v-if="currentOrder.status === 'confirmed'" class="modify-btn" @click="modifyOrder">
                        修改订单
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧区域：Dify AI对话框 -->
          <div class="right-panel">
            <div class="dify-chat-container">
              <div class="dify-chat-header">
                <div class="ai-header-content">
                  <h3>🤖 普普1.0 AI助手</h3>
                  <div class="dify-status" :class="{ 'connected': isDifyConnected, 'disconnected': !isDifyConnected }">
                    <span class="status-dot"></span>
                    <span class="status-text">{{ isDifyConnected ? 'Dify已连接' : '本地模式' }}</span>
                  </div>
                </div>
                <p>基于Dify平台的智能助手，为您提供专业的房间推荐和预订服务</p>
                <!-- 测试按钮 -->
                <div class="test-buttons" style="margin-top: 10px;">
                  <button @click="testDifyConnection" class="test-btn">测试连接</button>
                </div>
              </div>

              <!-- Dify聊天助手嵌入区域 -->
              <div class="dify-chat-wrapper" id="dify-chat-wrapper">
                <!-- 加载中提示 -->
                <div class="dify-loading" id="dify-loading" style="display: none;">
                  <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <p>正在加载AI助手...</p>
                  </div>
                </div>

                <!-- 备用聊天界面 -->
                <div class="fallback-chat" id="fallback-chat" style="display: flex;">
                  <div class="chat-messages" ref="fallbackChatContainer">
                    <div v-for="(message, index) in fallbackMessages" :key="index" :class="['message', message.type]">
                      <div class="message-content">
                        <div class="message-text" v-html="convertMarkdownLinks(message.content)"></div>
                        <div class="message-time">{{ message.timestamp }}</div>
                      </div>
                    </div>
                  </div>

                  <div class="chat-input-area">
                    <div class="input-container">
                      <input
                        v-model="fallbackInput"
                        @keyup.enter="sendFallbackMessage"
                        placeholder="请告诉我您的需求..."
                        class="chat-input"
                        :disabled="isFallbackLoading"
                      />
                      <button
                        @click="sendFallbackMessage"
                        class="send-btn"
                        :disabled="isFallbackLoading || !fallbackInput.trim()"
                      >
                        <i class="fas fa-paper-plane"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>


          </div>

          <!-- 关闭上半部分 -->
          </div>

          <!-- 下半部分：全部房间列表 -->
          <div class="bottom-section">
            <div class="all-rooms-container">
              <div class="all-rooms-header">
                <h3>🏠 全部房间</h3>
                <div class="rooms-controls">
                  <div class="view-toggle">
                    <button
                      :class="['toggle-btn', { active: viewMode === 'grid' }]"
                      @click="viewMode = 'grid'"
                    >
                      <i class="fas fa-th"></i>
                    </button>
                    <button
                      :class="['toggle-btn', { active: viewMode === 'list' }]"
                      @click="viewMode = 'list'"
                    >
                      <i class="fas fa-list"></i>
                    </button>
                  </div>
                  <div class="rooms-filter">
                    <select v-model="roomFilter.sortBy" @change="applyFilter">
                      <option value="price-asc">价格从低到高</option>
                      <option value="price-desc">价格从高到低</option>
                      <option value="name">按名称排序</option>
                      <option value="status">按状态排序</option>
                    </select>
                  </div>
                </div>
              </div>

              <div class="rooms-content">
                <div v-if="roomsLoading" class="rooms-loading">
                  <div class="loading-spinner"></div>
                  <p>正在加载房间信息...</p>
                </div>

                <div v-else-if="filteredRooms.length === 0" class="no-rooms">
                  <div class="empty-state">
                    <i class="fas fa-home"></i>
                    <p>暂无房间信息</p>
                  </div>
                </div>

                <div v-else :class="['rooms-list', viewMode]">
                  <div
                    v-for="room in filteredRooms"
                    :key="room.id"
                    :class="['room-card', { 'unavailable': room.status !== 'available' }]"
                    @click="selectRoom(room)"
                  >
                    <div class="room-image">
                      <img :src="room.image" :alt="room.name" />
                      <div class="room-status" :class="room.status">
                        {{ room.status === 'available' ? '可预订' : '已预订' }}
                      </div>

                    </div>
                    <div class="room-info">
                      <h4 class="room-name">{{ room.name }}</h4>
                      <p class="room-code">房间号: {{ room.code }}</p>
                      <p class="room-description">{{ room.describe }}</p>
                      <div class="room-details">
                        <span class="room-type">{{ room.category }}</span>
                        <span class="room-capacity">{{ room.seat }}人</span>
                        <span class="room-area">{{ room.area }}</span>
                      </div>
                      <div class="room-rating" v-if="room.rating">
                        <i class="fas fa-star"></i>
                        <span>{{ room.rating }}</span>
                      </div>
                      <div class="room-price">
                        <span class="price">¥{{ room.price }}</span>
                        <span class="unit">/晚</span>
                      </div>
                      <button
                        v-if="room.status === 'available'"
                        @click.stop="bookRoom(room)"
                        class="book-btn"
                      >
                        立即预订
                      </button>
                      <button
                        v-else
                        class="book-btn disabled"
                        disabled
                      >
                        已预订
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import request from '../../utils/request'
import AppNavbar from '../../components/AppNavbar.vue'
import { difyChatService } from '../../services/DifyChatService'

const router = useRouter()
const auth = useAuthStore()

// 响应式数据
const chatMessages = ref<any[]>([])
const userInput = ref('')
const isLoading = ref(false)
const chatContainer = ref<HTMLElement | null>(null)
const rooms = ref<any[]>([])
const filteredRooms = ref<any[]>([])
const roomsLoading = ref(false)
const roomTypes = ref<any[]>([])
const aiRecommendations = ref<any[]>([])
const totalAvailableRooms = ref(0)
const totalRooms = ref(0)

// 备用聊天相关
const fallbackInput = ref('')
const fallbackMessages = ref<any[]>([])
const isFallbackLoading = ref(false)
const isDifyLoaded = ref(false)
const isDifyConnected = ref(false)  // 新增：Dify连接状态
const fallbackChatContainer = ref<HTMLElement | null>(null)

// 完整的23间房间数据（按房间号顺序排列）
const mockRooms = ref([
  // 1楼房间 (101-108)
  {
    id: 1,
    name: '雨林景观豪华蘑菇屋',
    code: '101',
    category: '单人间',
    price: 18,
    rating: 4.9,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观2.jpg',
    area: '22㎡',
    seat: 1,
    address: '1楼东向',
    describe: '东向采光，带窗户，房间高度2.4米，超值特价单人蘑菇屋',
    vrUrl: 'https://vr.homestay.com/room/101'
  },
  {
    id: 2,
    name: '雨林景观豪华蘑菇屋',
    code: '102',
    category: '大床房',
    price: 888,
    rating: 4.9,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观1.jpg',
    area: '40㎡',
    seat: 2,
    address: '1楼东向',
    describe: '东向采光，带窗户，房间高度2.4米，豪华大床蘑菇屋',
    vrUrl: 'https://vr.homestay.com/room/102'
  },
  {
    id: 3,
    name: '雨林景观豪华蘑菇屋',
    code: '103',
    category: '单人间',
    price: 333,
    rating: 4.7,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观3.jpg',
    area: '22㎡',
    seat: 1,
    address: '1楼',
    describe: '无窗户设计，房间高度2.4米，精致的单人蘑菇屋'
  },
  {
    id: 4,
    name: '雨林景观豪华蘑菇屋',
    code: '104',
    category: '单人间',
    price: 255,
    rating: 4.6,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观3.jpg',
    area: '22㎡',
    seat: 1,
    address: '1楼',
    describe: '无窗户设计，房间高度2.4米，舒适的单人蘑菇屋'
  },
  {
    id: 5,
    name: '雨林景观豪华蘑菇屋',
    code: '105',
    category: '单人间',
    price: 256,
    rating: 4.7,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观3.jpg',
    area: '22㎡',
    seat: 1,
    address: '1楼',
    describe: '无窗户设计，房间高度2.4米，经济实惠的单人蘑菇屋'
  },
  {
    id: 6,
    name: '雨林景观豪华蘑菇屋',
    code: '106',
    category: '单人间',
    price: 666,
    rating: 4.8,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观2.jpg',
    area: '25㎡',
    seat: 1,
    address: '1楼南向',
    describe: '南向采光，带窗户，房间高度2.4米，高级单人蘑菇屋'
  },
  {
    id: 7,
    name: '雨林景观豪华蘑菇屋',
    code: '107',
    category: '单人间',
    price: 456,
    rating: 4.7,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观2.jpg',
    area: '22㎡',
    seat: 1,
    address: '1楼南向',
    describe: '南向采光，带窗户，房间高度2.4米，舒适单人蘑菇屋'
  },
  {
    id: 8,
    name: '雨林景观豪华蘑菇屋',
    code: '108',
    category: '单人间',
    price: 156,
    rating: 4.5,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观2.jpg',
    area: '22㎡',
    seat: 1,
    address: '1楼南向',
    describe: '南向采光，带窗户，房间高度2.4米，经济型单人蘑菇屋'
  },
  // 2楼房间 (201-212)
  {
    id: 9,
    name: '雨林景观豪华蘑菇屋',
    code: '201',
    category: '大床房',
    price: 19,
    rating: 4.8,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观1.jpg',
    area: '35㎡',
    seat: 2,
    address: '2楼南向',
    describe: '南向采光，带窗户，房间高度2.4米，超值特价大床蘑菇屋',
    vrUrl: 'https://vr.homestay.com/room/201'
  },
  {
    id: 10,
    name: '雨林景观豪华蘑菇屋',
    code: '202',
    category: '单人间',
    price: 48,
    rating: 4.6,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观2.jpg',
    area: '22㎡',
    seat: 1,
    address: '2楼南向',
    describe: '南向采光，带窗户，房间高度2.4米，特价单人蘑菇屋'
  },
  {
    id: 11,
    name: '雨林景观豪华蘑菇屋',
    code: '203',
    category: '单人间',
    price: 889,
    rating: 4.8,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观2.jpg',
    area: '25㎡',
    seat: 1,
    address: '2楼南向',
    describe: '南向采光，带窗户，房间高度2.4米，豪华单人蘑菇屋'
  },
  {
    id: 12,
    name: '雨林景观豪华蘑菇屋',
    code: '204',
    category: '单人间',
    price: 999,
    rating: 4.9,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观2.jpg',
    area: '25㎡',
    seat: 1,
    address: '2楼南向',
    describe: '南向采光，带窗户，房间高度2.4米，顶级单人蘑菇屋'
  },
  {
    id: 13,
    name: '雨林景观豪华蘑菇屋',
    code: '205',
    category: '双人间',
    price: 789,
    rating: 4.8,
    status: 'available',
    image: '/src/assets/images/环境展示/庄园内部2.jpg',
    area: '35㎡',
    seat: 2,
    address: '2楼东向',
    describe: '东向采光，带窗户，房间高度2.4米，豪华双人蘑菇屋'
  },
  {
    id: 14,
    name: '雨林景观豪华蘑菇屋',
    code: '206',
    category: '双人间',
    price: 654,
    rating: 4.7,
    status: 'available',
    image: '/src/assets/images/环境展示/庄园内部2.jpg',
    area: '32㎡',
    seat: 2,
    address: '2楼西向',
    describe: '西向采光，带窗户，房间高度2.4米，高级双人蘑菇屋'
  },
  {
    id: 15,
    name: '雨林景观豪华蘑菇屋',
    code: '207',
    category: '大床房',
    price: 186,
    rating: 4.5,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观1.jpg',
    area: '30㎡',
    seat: 2,
    address: '2楼东向',
    describe: '东向采光，带窗户，房间高度2.4米，经济型大床蘑菇屋'
  },
  {
    id: 16,
    name: '雨林景观豪华蘑菇屋',
    code: '208',
    category: '单人间',
    price: 18,
    rating: 4.9,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观2.jpg',
    area: '22㎡',
    seat: 1,
    address: '2楼北向',
    describe: '北向采光，带窗户，房间高度2.4米，超值特价单人蘑菇屋'
  },
  {
    id: 17,
    name: '雨林景观豪华蘑菇屋',
    code: '209',
    category: '单人间',
    price: 418,
    rating: 4.6,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观2.jpg',
    area: '22㎡',
    seat: 1,
    address: '2楼北向',
    describe: '北向采光，带窗户，房间高度2.4米，舒适单人蘑菇屋'
  },
  {
    id: 18,
    name: '雨林景观豪华蘑菇屋',
    code: '210',
    category: '单人间',
    price: 189,
    rating: 4.5,
    status: 'available',
    image: '/src/assets/images/实地调研/房间参观/房间参观2.jpg',
    area: '22㎡',
    seat: 1,
    address: '2楼北向',
    describe: '北向采光，带窗户，房间高度2.4米，经济型单人蘑菇屋'
  },
  {
    id: 19,
    name: '雨林景观豪华蘑菇屋',
    code: '211',
    category: '亲子房',
    price: 419,
    rating: 4.6,
    status: 'available',
    image: '/src/assets/images/card/card02.jpg',
    area: '45㎡',
    seat: 3,
    address: '2楼西向',
    describe: '西向采光，带窗户，房间高度2.4米，舒适的亲子蘑菇屋'
  },
  {
    id: 20,
    name: '雨林景观豪华蘑菇屋',
    code: '212',
    category: '亲子房',
    price: 418,
    rating: 4.6,
    status: 'available',
    image: '/src/assets/images/card/card01.jpg',
    area: '45㎡',
    seat: 3,
    address: '2楼西向',
    describe: '西向采光，带窗户，房间高度2.4米，温馨的亲子蘑菇屋'
  },
  // 3楼房间 (301-303)
  {
    id: 21,
    name: '雨林景观豪华蘑菇屋',
    code: '301',
    category: '亲子房',
    price: 486,
    rating: 4.6,
    status: 'available',
    image: '/src/assets/images/环境展示/庄园内部1.jpg',
    area: '45㎡',
    seat: 3,
    address: '3楼',
    describe: '无窗户设计，房间高度2.4米，适合家庭入住的温馨亲子蘑菇屋',
    vrUrl: 'https://vr.homestay.com/room/301'
  },
  {
    id: 22,
    name: '雨林景观豪华蘑菇屋',
    code: '302',
    category: '亲子房',
    price: 1088,
    rating: 5.0,
    status: 'available',
    image: '/src/assets/images/环境展示/庄园内部1.jpg',
    area: '50㎡',
    seat: 3,
    address: '3楼',
    describe: '无窗户设计，房间高度2.4米，顶级豪华亲子蘑菇屋'
  },
  {
    id: 23,
    name: '雨林景观豪华蘑菇屋',
    code: '303',
    category: '双人间',
    price: 388,
    rating: 4.8,
    status: 'available',
    image: '/src/assets/images/环境展示/庄园内部2.jpg',
    area: '32㎡',
    seat: 2,
    address: '3楼南向',
    describe: '南向采光，带窗户，房间高度2.4米，舒适的双人蘑菇屋'
  }
])

// 订单相关数据
const currentOrder = ref<any>(null)

// 语音识别相关
const isListening = ref(false)
const speechSupported = ref(false)
let recognition: any = null

// AI表情和语音相关
const currentExpression = ref('happy') // happy, thinking, excited, surprised, sad
const aiState = ref('idle') // idle, listening, thinking, speaking
const isSpeaking = ref(false)
const voiceEnabled = ref(false) // 默认关闭语音
const aiStatusText = ref('我会根据您的需求为您推荐最合适的房间')
let speechSynthesis: any = null

// AI角色图片URL - 使用茶茶正面IP形象
const aiImageUrl = ref('/src/assets/images/IP形象/茶茶正面.png')

// 房间筛选
const roomFilter = reactive({
  priceRange: '',
  type: '',
  sortBy: 'price-asc'
})

// 视图模式
const viewMode = ref('grid') // grid 或 list

// 初始化
onMounted(async () => {
  // 重置预订状态
  resetBookingState()

  await loadRooms()
  initSpeechRecognition()
  initSpeechSynthesis()

  // 直接显示备用聊天界面，不加载Dify嵌入式组件
  showFallbackChat()

  // 添加欢迎消息
  chatMessages.value.push({
    type: 'ai',
    content: '🍄 您好！我是普洱蘑菇庄园民宿的AI选房助手，请告诉我您的需求！',
    timestamp: new Date().toLocaleTimeString()
  })
})

onUnmounted(() => {
  if (recognition) {
    recognition.stop()
  }
})

// 加载房间数据
const loadRooms = async () => {
  roomsLoading.value = true
  try {
    // 使用模拟数据
    rooms.value = mockRooms.value
    applyFilter() // 应用筛选
    totalRooms.value = rooms.value.length
    totalAvailableRooms.value = rooms.value.filter(room => room.status === 'available').length
    generateRoomTypes()
  } catch (error) {
    console.error('加载房间数据失败:', error)
  } finally {
    roomsLoading.value = false
  }
}

// 应用房间筛选和排序
const applyFilter = () => {
  let filtered = [...rooms.value]

  // 根据排序方式排序
  switch (roomFilter.sortBy) {
    case 'price-asc':
      filtered.sort((a, b) => a.price - b.price)
      break
    case 'price-desc':
      filtered.sort((a, b) => b.price - a.price)
      break
    case 'name':
      filtered.sort((a, b) => a.name.localeCompare(b.name))
      break
    case 'status':
      filtered.sort((a, b) => {
        if (a.status === 'available' && b.status !== 'available') return -1
        if (a.status !== 'available' && b.status === 'available') return 1
        return 0
      })
      break
  }

  filteredRooms.value = filtered
}

// 生成房型分类数据
const generateRoomTypes = () => {
  const typeMap = new Map()
  
  rooms.value.forEach(room => {
    const typeName = room.name
    if (!typeMap.has(typeName)) {
      typeMap.set(typeName, {
        name: typeName,
        category: room.category,
        price: room.price,
        rating: room.rating,
        image: room.image,
        describe: room.describe,
        rooms: [],
        availableCount: 0,
        totalCount: 0,
        features: ['WiFi', '空调', '电视', '独立卫浴']
      })
    }
    
    const roomType = typeMap.get(typeName)
    roomType.rooms.push(room)
    roomType.totalCount++
    if (room.status === 'available') {
      roomType.availableCount++
    }
  })
  
  roomTypes.value = Array.from(typeMap.values())
}

// 表情相关函数
const getExpressionEmoji = () => {
  const expressions = {
    happy: '😊',
    thinking: '🤔',
    excited: '🤩',
    surprised: '😮',
    sad: '😔',
    love: '😍'
  }
  return expressions[currentExpression.value] || '😊'
}

const changeExpression = (expression: string) => {
  currentExpression.value = expression
}

const changeAIState = (state: string) => {
  aiState.value = state
}

// 语音相关函数
const initSpeechRecognition = () => {
  if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
    const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition
    recognition = new SpeechRecognition()
    recognition.continuous = false
    recognition.interimResults = false
    recognition.lang = 'zh-CN'

    recognition.onstart = () => {
      isListening.value = true
      changeAIState('listening')
      changeExpression('thinking')
    }

    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript
      userInput.value = transcript
      sendMessage()
    }

    recognition.onend = () => {
      isListening.value = false
      changeAIState('idle')
      changeExpression('happy')
    }

    recognition.onerror = (event: any) => {
      console.error('语音识别错误:', event.error)
      isListening.value = false
      changeAIState('idle')
      changeExpression('sad')
      setTimeout(() => changeExpression('happy'), 2000)
    }

    speechSupported.value = true
  }
}

const toggleVoiceInput = () => {
  if (!speechSupported.value) return

  if (isListening.value) {
    recognition.stop()
  } else {
    recognition.start()
  }
}

const initSpeechSynthesis = () => {
  if ('speechSynthesis' in window) {
    speechSynthesis = window.speechSynthesis
  }
}

const speakText = async (text: string) => {
  if (!speechSynthesis || !voiceEnabled.value) return

  return new Promise<void>((resolve) => {
    const utterance = new SpeechSynthesisUtterance(text)
    utterance.lang = 'zh-CN'
    utterance.rate = 0.9
    utterance.pitch = 1.1

    utterance.onstart = () => {
      isSpeaking.value = true
      changeAIState('speaking')
    }

    utterance.onend = () => {
      isSpeaking.value = false
      changeAIState('idle')
      resolve()
    }

    utterance.onerror = () => {
      isSpeaking.value = false
      changeAIState('idle')
      resolve()
    }

    speechSynthesis.speak(utterance)
  })
}

const toggleAIVoice = () => {
  voiceEnabled.value = !voiceEnabled.value
  if (voiceEnabled.value) {
    aiStatusText.value = '语音已开启，我会为您朗读回复'
  } else {
    aiStatusText.value = '我会根据您的需求为您推荐最合适的房间'
    if (speechSynthesis) {
      speechSynthesis.cancel()
    }
    isSpeaking.value = false
    changeAIState('idle')
  }
}

// Dify聊天助手初始化
const initDifyChatbot = () => {
  // 添加Dify样式覆盖
  addDifyStyles()

  // 设置Dify配置
  ;(window as any).difyChatbotConfig = {
    token: 'fggmGdSFt6MSQFJa',
    baseUrl: 'http://4295a4ce.r28.cpolar.top'
  }

  // 动态加载Dify脚本
  const script = document.createElement('script')
  script.src = 'http://4295a4ce.r28.cpolar.top/embed.min.js'
  script.id = 'fggmGdSFt6MSQFJa'
  script.defer = true

  script.onload = () => {
    console.log('Dify聊天助手加载成功')
    // 等待DOM更新后自定义聊天助手
    setTimeout(() => {
      customizeDifyChatbot()
    }, 1000)
  }

  script.onerror = () => {
    console.error('Dify聊天助手脚本加载失败，显示备用聊天界面')
    showFallbackChat()
  }

  document.head.appendChild(script)
}

// 添加Dify样式覆盖
const addDifyStyles = () => {
  const style = document.createElement('style')
  style.textContent = `
    #dify-chatbot-bubble-button {
      display: none !important;
    }
    #dify-chatbot-bubble-window {
      position: static !important;
      bottom: auto !important;
      right: auto !important;
      width: 100% !important;
      height: 100% !important;
      max-width: none !important;
      max-height: none !important;
      border: none !important;
      border-radius: 0 !important;
      box-shadow: none !important;
      background: transparent !important;
      transform: none !important;
      z-index: auto !important;
    }
  `
  document.head.appendChild(style)
}

// 自定义Dify聊天助手样式和位置
const customizeDifyChatbot = () => {
  console.log('开始自定义Dify聊天助手')

  // 隐藏默认的聊天气泡按钮
  const bubbleButton = document.getElementById('dify-chatbot-bubble-button')
  if (bubbleButton) {
    bubbleButton.style.display = 'none'
    console.log('已隐藏聊天气泡按钮')
  }

  // 查找聊天窗口
  let chatWindow = document.getElementById('dify-chatbot-bubble-window')
  if (chatWindow) {
    moveChatWindowToContainer(chatWindow)
  } else {
    // 如果聊天窗口还没有创建，尝试触发创建
    if (bubbleButton) {
      console.log('尝试触发聊天窗口创建')
      bubbleButton.click()
      setTimeout(() => {
        chatWindow = document.getElementById('dify-chatbot-bubble-window')
        if (chatWindow) {
          moveChatWindowToContainer(chatWindow)
        }
      }, 500)
    }
  }

  // 定期检查聊天窗口是否出现
  let checkCount = 0
  const checkInterval = setInterval(() => {
    checkCount++
    const chatWindow = document.getElementById('dify-chatbot-bubble-window')
    if (chatWindow) {
      clearInterval(checkInterval)
      moveChatWindowToContainer(chatWindow)
    } else if (checkCount > 20) {
      clearInterval(checkInterval)
      console.warn('聊天窗口未能正确加载，显示备用聊天界面')
      showFallbackChat()
    }
  }, 500)

  // 设置总体超时，如果10秒后还没有加载成功，就显示备用界面
  setTimeout(() => {
    if (!isDifyLoaded.value) {
      console.warn('Dify加载超时，显示备用聊天界面')
      showFallbackChat()
    }
  }, 10000)
}

// 移动聊天窗口到指定容器
const moveChatWindowToContainer = (chatWindow: HTMLElement) => {
  console.log('移动聊天窗口到容器中')

  const wrapper = document.getElementById('dify-chat-wrapper')
  if (wrapper) {
    // 隐藏加载提示
    const loading = document.getElementById('dify-loading')
    if (loading) {
      loading.style.display = 'none'
    }

    // 隐藏备用聊天
    const fallbackChat = document.getElementById('fallback-chat')
    if (fallbackChat) {
      fallbackChat.style.display = 'none'
    }

    // 重置聊天窗口样式 - 完全覆盖容器
    chatWindow.style.position = 'absolute'
    chatWindow.style.top = '0'
    chatWindow.style.left = '0'
    chatWindow.style.bottom = '0'
    chatWindow.style.right = '0'
    chatWindow.style.width = '100%'
    chatWindow.style.height = '100%'
    chatWindow.style.maxWidth = 'none'
    chatWindow.style.maxHeight = 'none'
    chatWindow.style.border = 'none'
    chatWindow.style.borderRadius = '20px'
    chatWindow.style.zIndex = '10'
    chatWindow.style.borderRadius = '20px'  /* 与容器圆角一致 */
    chatWindow.style.boxShadow = 'none'
    chatWindow.style.display = 'block'
    chatWindow.style.transform = 'none'
    chatWindow.style.zIndex = '10'  /* 确保在最上层 */

    // 移动到我们的容器 - 让Dify对话框完全覆盖
    wrapper.appendChild(chatWindow)
    isDifyLoaded.value = true
    console.log('聊天窗口移动完成')
  }
}

// 显示备用聊天界面
const showFallbackChat = () => {
  console.log('显示备用聊天界面')

  // 隐藏加载提示
  const loading = document.getElementById('dify-loading')
  if (loading) {
    loading.style.display = 'none'
  }

  // 显示备用聊天
  const fallbackChat = document.getElementById('fallback-chat')
  if (fallbackChat) {
    fallbackChat.style.display = 'flex'
  }

  // 添加欢迎消息
  if (fallbackMessages.value.length === 0) {
    fallbackMessages.value.push({
      type: 'ai',
      content: '🤖 您好！我是普普1.0，基于Dify平台的AI助手。我可以为您提供房间推荐、预订服务和各种咨询。请告诉我您的需求！',
      timestamp: new Date().toLocaleTimeString()
    })
  }
}

// 发送备用聊天消息
const sendFallbackMessage = async () => {
  if (!fallbackInput.value.trim() || isFallbackLoading.value) return

  const message = fallbackInput.value.trim()
  fallbackInput.value = ''

  // 添加用户消息
  fallbackMessages.value.push({
    type: 'user',
    content: message,
    timestamp: new Date().toLocaleTimeString()
  })

  isFallbackLoading.value = true

  try {
    // 首先尝试调用后端Dify集成API
    console.log('🔄 正在调用Dify API...', message)

    // 尝试两种方式：流式和非流式
    let useStreamingResponse = true

    const response = await fetch('http://localhost:8080/chat/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: message,
        user: 'ai-room-user',
        inputs: {}
      })
    })

    console.log('📡 Dify API响应状态:', response.status, response.ok)

    if (response.ok) {
      // 标记Dify连接成功
      isDifyConnected.value = true
      console.log('✅ Dify连接成功，开始处理流式响应')

      // 处理SSE流式响应
      const reader = response.body?.getReader()
      if (reader) {
        let aiResponse = ''

        // 添加AI消息占位符
        const aiMessageIndex = fallbackMessages.value.length
        fallbackMessages.value.push({
          type: 'ai',
          content: '🤖 Dify正在为您查询...',
          timestamp: new Date().toLocaleTimeString()
        })

        console.log('📖 开始读取流式数据...')

        // 读取流式数据
        let buffer = ''
        let messageCount = 0
        console.log('📖 开始读取流式数据...')

        while (true) {
          console.log('🔄 尝试读取下一个数据块...')
          const { done, value } = await reader.read()
          console.log('📥 读取结果:', { done, valueLength: value?.length })

          if (done) {
            console.log('📖 流式数据读取完成')
            break
          }

          const chunk = new TextDecoder().decode(value)
          buffer += chunk
          console.log('📦 收到数据块:', chunk.substring(0, 100) + (chunk.length > 100 ? '...' : ''))

          // 处理完整的行
          const lines = buffer.split('\n')
          buffer = lines.pop() || '' // 保留最后一个不完整的行

          for (const line of lines) {
            // 处理标准格式 "data: " 和异常格式 "data:data: "
            let data = ''
            if (line.startsWith('data:data: ')) {
              data = line.slice(11).trim() // 去掉 "data:data: "
              console.log('📄 处理异常格式数据行:', data.substring(0, 100) + (data.length > 100 ? '...' : ''))
            } else if (line.startsWith('data: ')) {
              data = line.slice(6).trim() // 去掉 "data: "
              console.log('📄 处理标准格式数据行:', data.substring(0, 100) + (data.length > 100 ? '...' : ''))
            }

            if (data && data !== '[DONE]') {
              try {
                // 解析Dify的JSON响应
                const jsonData = JSON.parse(data)
                  console.log('📋 解析JSON成功:', {
                    event: jsonData.event,
                    hasAnswer: !!jsonData.answer,
                    answerLength: jsonData.answer ? jsonData.answer.length : 0,
                    answer: jsonData.answer,
                    fullData: jsonData
                  })

                  // 处理所有包含answer字段的事件
                  if (jsonData.answer && jsonData.answer.trim()) {
                    console.log('✅ 收到消息片段:', jsonData.answer)
                    aiResponse += jsonData.answer
                    messageCount++
                    // 实时更新AI消息
                    fallbackMessages.value[aiMessageIndex].content = aiResponse

                    // 实时解析房间推荐（每次收到新片段时都尝试解析）
                    parseRoomRecommendations(aiResponse)

                    scrollFallbackToBottom()
                  }

                  // 处理消息结束事件
                  if (jsonData.event === 'message_end') {
                    console.log('✅ AI消息结束，最终响应长度:', aiResponse.length, '消息片段数:', messageCount)
                    if (aiResponse.trim()) {
                      fallbackMessages.value[aiMessageIndex].content = aiResponse
                      // 检查Dify响应中的预订意图
                      if (bookingState.value.step !== 'none') {
                        handleBookingFlow(message)
                      } else {
                        parseRoomRecommendations(aiResponse)
                        handleOrderProcessing(aiResponse, message)
                      }
                    } else {
                      // 如果没有收到内容，显示一个友好的消息
                      fallbackMessages.value[aiMessageIndex].content = '🤖 AI已处理您的请求，但响应内容为空。请尝试重新提问。'
                    }
                    scrollFallbackToBottom()
                  }

                  // 记录所有事件类型
                  if (jsonData.event !== 'message') {
                    console.log('📝 事件类型:', jsonData.event, jsonData.answer ? `包含答案: ${jsonData.answer.substring(0, 50)}...` : '无答案')
                  }
              } catch (e) {
                console.log('❌ JSON解析失败，作为文本处理:', e.message, '原始数据:', data)
                // 如果不是JSON格式，直接添加文本（兼容本地AI）
                aiResponse += data
                fallbackMessages.value[aiMessageIndex].content = aiResponse
                scrollFallbackToBottom()
              }
            } else if (data === '') {
              console.log('📝 收到空数据行')
            } else if (line.trim()) {
              console.log('📝 非data行:', line)
            }
          }
        }

        // 处理剩余的缓冲区内容
        if (buffer.trim()) {
          console.log('📝 处理剩余缓冲区内容:', buffer.trim())
          const line = buffer.trim()
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim()
            if (data && data !== '[DONE]') {
              try {
                const jsonData = JSON.parse(data)
                if (jsonData.event === 'message' && jsonData.answer) {
                  aiResponse += jsonData.answer
                  fallbackMessages.value[aiMessageIndex].content = aiResponse
                  scrollFallbackToBottom()
                }
              } catch (e) {
                console.log('❌ 缓冲区JSON解析失败，作为文本处理')
                aiResponse += data
                fallbackMessages.value[aiMessageIndex].content = aiResponse
                scrollFallbackToBottom()
              }
            }
          }
        }

        // 如果没有收到响应，使用降级回复
        if (!aiResponse.trim()) {
          console.log('⚠️ 未收到Dify响应，使用本地降级回复')
          console.log('📊 调试信息 - 消息片段数:', messageCount, '缓冲区内容:', buffer)

          // 优先处理预订流程，即使Dify有部分响应
          if (bookingState.value.step !== 'none') {
            console.log('🔄 在预订流程中，使用本地处理')
            handleBookingFlow(message)
          } else if (messageCount > 0 || buffer.includes('message')) {
            // 如果有Dify响应但解析失败，显示提示并尝试本地处理
            fallbackMessages.value[aiMessageIndex].content = '🤖 AI正在为您处理请求...'

            // 延迟处理，给Dify更多时间
            setTimeout(() => {
              if (!aiResponse.trim()) {
                const localResponse = generateLocalAIResponse(message)
                fallbackMessages.value[aiMessageIndex].content = localResponse
                parseRoomRecommendations(localResponse)
                handleOrderProcessing(localResponse, message)
              }
            }, 2000)
          } else {
            // 完全降级到本地处理
            const localResponse = generateLocalAIResponse(message)
            fallbackMessages.value[aiMessageIndex].content = localResponse
            parseRoomRecommendations(localResponse)
            handleOrderProcessing(localResponse, message)
          }
        } else {
          // 解析AI响应中的房间推荐
          parseRoomRecommendations(aiResponse)
          // 检查是否包含订单处理请求
          handleOrderProcessing(aiResponse, message)
          console.log('✅ Dify响应处理完成，总长度:', aiResponse.length)
        }
        return
      }
    }

    // 如果Dify API调用失败，降级到本地回复
    throw new Error('Dify API调用失败')

  } catch (error) {
    console.error('❌ 调用Dify API过程中出现错误:', error)

    // 检查是否已经有部分响应
    const existingMessages = fallbackMessages.value.filter(msg => msg.content.includes('Dify正在为您查询'))
    if (existingMessages.length > 0) {
      console.log('⚠️ 流式处理中断，但可能已有部分响应')
      // 不要覆盖已有的消息，让用户看到已收到的内容
      return
    }

    // 标记Dify连接失败
    isDifyConnected.value = false

    // 降级到本地AI回复
    console.log('🔄 完全降级到本地AI回复')

    // 检查是否在预订流程中
    if (bookingState.value.step !== 'none') {
      handleBookingFlow(message)
    } else {
      const aiResponse = generateLocalAIResponse(message)
      fallbackMessages.value.push({
        type: 'ai',
        content: aiResponse,
        timestamp: new Date().toLocaleTimeString()
      })

      // 解析AI响应中的房间推荐
      parseRoomRecommendations(aiResponse)
      // 检查是否包含订单处理请求
      handleOrderProcessing(aiResponse, message)
    }
  } finally {
    isFallbackLoading.value = false
    scrollFallbackToBottom()
  }
}

// 滚动备用聊天到底部
const scrollFallbackToBottom = () => {
  nextTick(() => {
    const container = fallbackChatContainer.value
    if (container) {
      container.scrollTop = container.scrollHeight
    }
  })
}

// 测试Dify连接
const testDifyConnection = async () => {
  console.log('测试Dify连接...')
  try {
    await fetch('http://4295a4ce.r28.cpolar.top/embed.min.js', {
      method: 'HEAD',
      mode: 'no-cors'
    })
    console.log('Dify连接测试成功')
    alert('Dify连接测试成功！')
  } catch (error) {
    console.error('Dify连接测试失败:', error)
    alert('Dify连接测试失败：' + error.message)
  }
}

// AI聊天相关 - 现在主要用于左侧推荐逻辑
const sendMessage = async () => {
  // 这个函数现在主要用于处理左侧推荐逻辑
  // 实际的聊天功能由Dify聊天助手处理
  console.log('聊天功能已由Dify聊天助手接管')
}

const getAIResponse = async (message: string) => {
  console.log('🤖 AI选房页面获取AI响应:', message)

  // 检查是否启用Dify
  if (aiConfig.features.enableDify) {
    try {
      // 尝试使用Dify服务
      console.log('🔄 尝试调用Dify服务...')
      const stream = await difyChatService.sendMessage(message, 'ai-room-user')
      let fullResponse = ''

      // 处理流式响应
      for await (const chunk of difyChatService.processStreamResponse(stream)) {
        fullResponse += chunk
      }

      console.log('✅ Dify响应:', fullResponse)

      // 如果Dify返回有效响应，使用它
      if (fullResponse && fullResponse.trim()) {
        return fullResponse.trim()
      } else {
        console.warn('⚠️ Dify返回空响应，降级到本地响应')
        return generateIntelligentLocalResponse(message)
      }
    } catch (error) {
      console.error('❌ Dify服务调用失败:', error)
      console.log('🔄 降级到智能本地AI响应')
      return generateIntelligentLocalResponse(message)
    }
  } else {
    console.log('🔄 Dify已禁用，使用智能本地AI响应')
    return generateIntelligentLocalResponse(message)
  }
}

// 转换Markdown链接为HTML链接
const convertMarkdownLinks = (content: string) => {
  if (!content) return content

  // 将Markdown格式的链接 [文本](URL) 转换为HTML链接
  return content.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" style="color: #007bff; text-decoration: underline;">$1</a>')
}

// 智能本地AI响应生成器
const generateIntelligentLocalResponse = (message: string) => {
  const lowerMessage = message.toLowerCase()
  console.log('🤖 生成智能本地响应:', message)

  // 1. 推荐房间 - 智能分析用户需求
  if (lowerMessage.includes('推荐') || lowerMessage.includes('房间') ||
      lowerMessage.includes('选房') || lowerMessage.includes('住宿')) {

    changeExpression('excited')
    setTimeout(() => changeExpression('happy'), 3000)

    const availableRooms = mockRooms.value.filter(room => room.status === 'available').slice(0, 3)

    if (availableRooms.length === 0) {
      return '🍄 抱歉，目前没有可用房间。请稍后再试或联系客服。'
    }

    let response = '🤔 让我为您分析一下...\n\n'
    response += '🍄 根据您的需求，我为您精心推荐以下房间：\n\n'

    availableRooms.forEach((room, index) => {
      response += `🏠 **${room.code}号房** - ${room.name}\n`
      response += `💰 价格：${room.price}元/晚\n`
      response += `🛏️ 类型：${room.category} | 📐 面积：${room.area}\n`

      // 添加智能推荐理由
      if (room.price < 50) {
        response += `✨ 推荐理由：超值性价比，适合预算有限的旅客\n`
      } else if (room.price > 500) {
        response += `✨ 推荐理由：豪华配置，适合追求品质的客人\n`
      } else {
        response += `✨ 推荐理由：价格适中，舒适体验\n`
      }

      if (room.vrUrl) {
        response += `🔗 [查看VR房间](${room.vrUrl})\n\n`
      } else {
        response += '\n'
      }
    })

    response += '💡 您可以告诉我您的具体需求（如预算、房型偏好），我可以为您做更精准的推荐！'
    return response
  }

  // 2. 楼层查询 - 智能分析
  if (lowerMessage.includes('2楼') || lowerMessage.includes('二楼') || lowerMessage.includes('楼')) {
    changeExpression('thinking')
    setTimeout(() => changeExpression('happy'), 2000)

    const floor2Rooms = mockRooms.value.filter(room =>
      room.code.startsWith('2') && room.status === 'available'
    )

    if (floor2Rooms.length === 0) {
      return '🏢 抱歉，2楼目前没有可用房间。让我为您推荐其他楼层的优质房间吧！'
    }

    let response = '🤔 我理解您对2楼房间的偏好...\n\n'
    response += '🏢 **2楼房间的优势**：\n• 视野更开阔\n• 相对安静\n• 采光更好\n• 可俯瞰茶园\n\n'
    response += '🍄 **2楼可用房间**：\n\n'

    floor2Rooms.slice(0, 3).forEach((room, index) => {
      response += `${index + 1}. **${room.code}号房** - ${room.name}\n`
      response += `   💰 ${room.price}元/晚 | 🛏️ ${room.category}\n\n`
    })

    response += '您比较倾向于哪个价位区间呢？我可以为您详细介绍！'
    return response
  }

  // 3. 预订确认 - 智能处理
  if (lowerMessage.includes('预订') || lowerMessage.includes('要') || lowerMessage.includes('选择')) {
    changeExpression('love')
    setTimeout(() => changeExpression('happy'), 3000)

    // 提取房间号
    const roomMatch = lowerMessage.match(/(\d{3})/);
    if (roomMatch) {
      const roomCode = roomMatch[1];
      const room = mockRooms.value.find(room => room.code === roomCode);
      if (room) {
        return `🎉 太好了！${room.code}号房是很棒的选择！\n\n` +
               `🏠 **房间信息确认**\n` +
               `• 房间号：${room.code}\n` +
               `• 房型：${room.name}\n` +
               `• 价格：${room.price}元/晚\n` +
               `• 类型：${room.category}\n` +
               `• 面积：${room.area}\n\n` +
               `💭 **我正在为您处理预订...**\n\n` +
               `📅 请告诉我您的入住信息：\n` +
               `• 入住日期和退房日期\n` +
               `• 入住人数\n\n` +
               `例如："入住2025-07-15，退房2025-07-17，2人入住"`
      }
    }
    return '🍄 请告诉我您想预订哪个房间号，比如"预订201"。'
  }

  // 4. 问候语 - 智能回复
  if (lowerMessage.includes('你好') || lowerMessage.includes('您好') || lowerMessage.includes('hello')) {
    changeExpression('happy')
    return '🍄 您好！欢迎来到普洱蘑菇庄园民宿！\n\n' +
           '🤖 我是您的专属AI选房助手，虽然目前使用的是本地智能模式，但我依然可以为您提供：\n\n' +
           '• 🏠 智能房间推荐\n' +
           '• 📅 预订服务\n' +
           '• 🔍 房间查询\n' +
           '• 💡 个性化建议\n\n' +
           '请告诉我您的需求，我会尽力为您提供最好的服务！'
  }

  // 5. 默认智能回复
  changeExpression('thinking')
  setTimeout(() => changeExpression('happy'), 2000)

  return `🤔 让我思考一下您的问题："${message}"\n\n` +
         `💭 **分析中...** 我理解您可能想了解：\n` +
         `• 房间信息和预订\n` +
         `• 民宿特色和服务\n` +
         `• 价格和优惠\n\n` +
         `🍄 虽然我目前使用本地智能模式，但我会尽力为您提供帮助！\n\n` +
         `您可以尝试说：\n` +
         `• "推荐房间"\n` +
         `• "有没有2楼的房间"\n` +
         `• "预订201房间"`
}

// 保持向后兼容
const generateLocalAIResponse = generateIntelligentLocalResponse

// 解析AI响应中的房间推荐
const parseRoomRecommendations = (aiResponse: string) => {
  console.log('🔍 开始解析房间推荐:', aiResponse)

  // 提取房间号的正则表达式 - 支持多种格式
  const roomNumberRegex = /(\d{3})号?房|房间号?[：:]?\s*(\d{3})|(\d{3})\s*号/g
  const roomNumbers: string[] = []
  let match: RegExpExecArray | null

  while ((match = roomNumberRegex.exec(aiResponse)) !== null) {
    // 获取匹配的房间号（可能在不同的捕获组中）
    const roomNumber = match[1] || match[2] || match[3]
    if (roomNumber && !roomNumbers.includes(roomNumber)) {
      roomNumbers.push(roomNumber)
    }
  }

  console.log('🏠 提取到的房间号:', roomNumbers)

  if (roomNumbers.length > 0) {
    // 清空当前推荐
    aiRecommendations.value = []

    // 根据房间号从mockRooms中找到对应房间
    roomNumbers.forEach((roomCode) => {
      const room = mockRooms.value.find(r => r.code === roomCode)
      if (room) {
        aiRecommendations.value.push({
          ...room,
          reason: `AI推荐：${room.category} ${room.price}元/晚`
        })
        console.log(`✅ 添加推荐房间: ${roomCode}号房`)
      } else {
        console.log(`⚠️ 未找到房间: ${roomCode}号房`)
      }
    })

    console.log('🎯 最终AI推荐房间:', aiRecommendations.value)
  } else {
    // 如果没有找到具体房间号，但AI在推荐房间，显示通用推荐
    if (aiResponse.includes('推荐') || aiResponse.includes('建议') || aiResponse.includes('适合')) {
      console.log('🤖 AI在推荐但未找到具体房间号，显示热门推荐')

      // 显示几个热门房间作为推荐
      const popularRooms = [
        mockRooms.value.find(r => r.code === '201'), // 超值大床房
        mockRooms.value.find(r => r.code === '101'), // 超值单人间
        mockRooms.value.find(r => r.code === '301')  // 亲子房
      ].filter(Boolean)

      aiRecommendations.value = popularRooms.map(room => ({
        ...room,
        reason: 'AI推荐：热门房型'
      }))

      console.log('🎯 显示热门推荐房间:', aiRecommendations.value)
    }
  }
}

// 处理订单相关请求
const handleOrderProcessing = (aiResponse: string, userMessage: string) => {
  console.log('🛒 检查订单处理请求:', { aiResponse, userMessage, currentStep: bookingState.value.step })

  // 如果已经在预订流程中，不要重复触发
  if (bookingState.value.step !== 'none') {
    console.log('⚠️ 已在预订流程中，跳过重复检测')
    return
  }

  // 只检查用户消息中的预订意图，不检查AI回复
  // 避免AI推荐房间时误触发预订流程
  const bookingKeywords = ['预订', '订房', '预定', '下单', '要订', '我要', '选择', '入住']
  const hasBookingIntent = bookingKeywords.some(keyword =>
    userMessage.toLowerCase().includes(keyword)
  )

  console.log('🔍 预订意图检测:', { hasBookingIntent, userMessage, 检测范围: '仅用户消息' })

  if (hasBookingIntent) {
    // 只从用户消息中提取房间号，不从AI回复中提取
    const roomNumberPatterns = [
      /预订?(\d{3})号?房/,      // 预订102号房
      /预定(\d{3})号?房/,       // 预定102号房
      /我要(\d{3})号?房/,       // 我要102号房
      /入住(\d{3})号?房/,       // 入住102号房
      /选择(\d{3})号?房/,       // 选择102号房
      /(\d{3})号?房.*预订/,     // 102号房预订
      /(\d{3})号?房.*预定/,     // 102号房预定
      /(\d{3})号?房.*入住/      // 102号房入住
    ]

    let roomNumberMatch = null
    let roomCode = ''

    for (const pattern of roomNumberPatterns) {
      roomNumberMatch = userMessage.match(pattern)
      if (roomNumberMatch) {
        roomCode = roomNumberMatch[1]
        console.log(`🎯 检测到预订意图，房间号: ${roomCode}，匹配模式:`, pattern)
        break
      }
    }

    if (roomCode) {
      // 找到对应房间
      const room = mockRooms.value.find(r => r.code === roomCode)
      if (room) {
        console.log('✅ 找到房间，开始预订流程:', room)
        // 开始预订流程
        startBookingProcess(room)
      } else {
        console.log('❌ 未找到房间:', roomCode)
      }
    } else {
      console.log('❌ 未能提取房间号，用户消息:', userMessage)
      console.log('💡 提示：请明确说出要预订的房间号，如"预订105号房"')
    }
  }
}

// 预订状态管理
const bookingState = ref({
  step: 'none', // none, room_selected, date_input, confirm
  selectedRoom: null,
  checkInDate: '',
  checkOutDate: '',
  days: 0,
  totalPrice: 0
})

// 重置预订状态
const resetBookingState = () => {
  bookingState.value = {
    step: 'none',
    selectedRoom: null,
    checkInDate: '',
    checkOutDate: '',
    days: 0,
    totalPrice: 0
  }
  console.log('🔄 预订状态已重置')
}

// AI预订流程 - 第一步：选择房间
const startBookingProcess = (room: any) => {
  console.log('📝 开始预订流程:', room)

  bookingState.value = {
    step: 'date_input',
    selectedRoom: room,
    checkInDate: '',
    checkOutDate: '',
    days: 0,
    totalPrice: 0
  }

  // 询问入住日期
  fallbackMessages.value.push({
    type: 'ai',
    content: `🏠 您选择了${room.code}号房 - ${room.name}\n` +
             `💰 价格：${room.price}元/晚\n\n` +
             `请告诉我您的入住和退房日期：\n` +
             `📅 格式示例：\n` +
             `• "入住2025-07-15，退房2025-07-17"\n` +
             `• "明天入住，后天退房"\n` +
             `• "7月15日到7月17日"`,
    timestamp: new Date().toLocaleTimeString()
  })

  scrollFallbackToBottom()
}

// AI预订流程 - 第二步：处理日期输入
const handleDateInput = (message: string) => {
  console.log('🔍 开始解析日期输入:', message)
  const lowerMessage = message.toLowerCase()

  // 解析日期的正则表达式
  const datePatterns = [
    /入住(\d{4}-\d{1,2}-\d{1,2}).*?退房(\d{4}-\d{1,2}-\d{1,2})/,
    /(\d{4}-\d{1,2}-\d{1,2}).*?到.*?(\d{4}-\d{1,2}-\d{1,2})/,
    /(\d{1,2})月(\d{1,2})日.*?到.*?(\d{1,2})月(\d{1,2})日/,
    /(\d{1,2})月(\d{1,2})日.*?到.*?(\d{1,2})日/,  // 支持"7月15日到17日"格式
    /(\d{1,2})月(\d{1,2})日.*?(\d{1,2})日/        // 支持"7月15日到17日"格式（更简洁）
  ]

  let checkIn = ''
  let checkOut = ''

  // 尝试解析标准日期格式
  for (let i = 0; i < datePatterns.length; i++) {
    const pattern = datePatterns[i]
    const match = message.match(pattern)
    if (match) {
      if (i === 2) {
        // 处理完整月日格式：7月15日到7月17日
        const year = new Date().getFullYear()
        checkIn = `${year}-${match[1].padStart(2, '0')}-${match[2].padStart(2, '0')}`
        checkOut = `${year}-${match[3].padStart(2, '0')}-${match[4].padStart(2, '0')}`
      } else if (i === 3 || i === 4) {
        // 处理简化月日格式：7月15日到17日
        const year = new Date().getFullYear()
        const month = match[1]
        checkIn = `${year}-${month.padStart(2, '0')}-${match[2].padStart(2, '0')}`
        checkOut = `${year}-${month.padStart(2, '0')}-${match[3].padStart(2, '0')}`
      } else {
        // 处理标准格式
        checkIn = match[1]
        checkOut = match[2]
      }
      console.log('🔍 日期解析成功:', { pattern: i, checkIn, checkOut, match })
      break
    }
  }

  // 处理相对日期
  if (!checkIn && (lowerMessage.includes('明天') || lowerMessage.includes('后天'))) {
    const today = new Date()
    if (lowerMessage.includes('明天入住')) {
      checkIn = new Date(today.getTime() + 24 * 60 * 60 * 1000).toISOString().slice(0, 10)
    }
    if (lowerMessage.includes('后天退房')) {
      checkOut = new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10)
    }
  }

  if (checkIn && checkOut) {
    const checkInDate = new Date(checkIn)
    const checkOutDate = new Date(checkOut)
    const days = Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (24 * 60 * 60 * 1000))

    if (days > 0) {
      bookingState.value.checkInDate = checkIn
      bookingState.value.checkOutDate = checkOut
      bookingState.value.days = days
      bookingState.value.totalPrice = bookingState.value.selectedRoom.price * days
      bookingState.value.step = 'confirm'

      // 显示确认信息
      fallbackMessages.value.push({
        type: 'ai',
        content: `📋 请确认您的预订信息：\n\n` +
                 `🏠 房间：${bookingState.value.selectedRoom.code}号房 - ${bookingState.value.selectedRoom.name}\n` +
                 `📅 入住日期：${checkIn}\n` +
                 `📅 退房日期：${checkOut}\n` +
                 `🌙 住宿天数：${days}晚\n` +
                 `💰 房间价格：${bookingState.value.selectedRoom.price}元/晚\n` +
                 `💳 总价：${bookingState.value.totalPrice}元\n\n` +
                 `请回复"确认预订"来完成预订，或"取消"来重新选择。`,
        timestamp: new Date().toLocaleTimeString()
      })
    } else {
      fallbackMessages.value.push({
        type: 'ai',
        content: `❌ 日期有误，退房日期必须晚于入住日期。\n请重新输入日期。`,
        timestamp: new Date().toLocaleTimeString()
      })
    }
  } else {
    console.log('❌ 日期解析失败，原始输入:', message)
    fallbackMessages.value.push({
      type: 'ai',
      content: `❌ 无法识别日期格式："${message}"\n请按以下格式输入：\n• "入住2025-07-15，退房2025-07-17"\n• "7月15日到7月17日"\n• "明天入住，后天退房"`,
      timestamp: new Date().toLocaleTimeString()
    })
  }

  scrollFallbackToBottom()
}

// AI预订流程 - 第三步：确认预订
const confirmBooking = () => {
  const room = bookingState.value.selectedRoom

  // 生成订单号
  const orderNo = 'ORD' + new Date().toISOString().slice(0, 10).replace(/-/g, '') +
                  Math.random().toString().slice(2, 5)

  // 创建订单数据 - 字段名与模板匹配
  const orderData = {
    orderNumber: orderNo,           // 模板使用 orderNumber
    roomId: room.id,
    roomCode: room.code,
    roomName: room.name,
    category: room.category,
    roomPrice: room.price,          // 模板使用 roomPrice
    checkInDate: bookingState.value.checkInDate,   // 模板使用 checkInDate
    checkOutDate: bookingState.value.checkOutDate, // 模板使用 checkOutDate
    nights: bookingState.value.days,               // 模板使用 nights
    people: room.seat,
    totalAmount: bookingState.value.totalPrice,    // 模板使用 totalAmount
    status: 'pending',              // 使用标准状态值
    createTime: new Date().toLocaleString()
  }

  // 添加到订单详情（左下角第三个框）
  currentOrder.value = orderData

  // 添加确认消息到聊天
  fallbackMessages.value.push({
    type: 'ai',
    content: `✅ 订单创建成功！\n\n📋 订单详情：\n• 订单号：${orderData.orderNumber}\n• 房间：${room.code}号房 (${room.category})\n• 入住日期：${orderData.checkInDate}\n• 退房日期：${orderData.checkOutDate}\n• 住宿天数：${orderData.nights}晚\n• 总价：${orderData.totalAmount}元\n\n您的房间已预订成功，请按时入住！\n订单详情已显示在左下角。`,
    timestamp: new Date().toLocaleTimeString()
  })

  // 重置预订状态
  resetBookingState()

  console.log('✅ 订单创建完成:', orderData)
  scrollFallbackToBottom()
}

// 防止重复消息的时间戳
let lastMessageTime = 0

// 处理预订流程中的消息
const handleBookingFlow = (message: string) => {
  const now = Date.now()

  // 防止短时间内重复处理相同消息
  if (now - lastMessageTime < 1000) {
    console.log('⚠️ 防止重复处理消息，跳过')
    return
  }
  lastMessageTime = now

  const lowerMessage = message.toLowerCase()

  if (bookingState.value.step === 'date_input') {
    // 处理日期输入
    handleDateInput(message)
  } else if (bookingState.value.step === 'confirm') {
    // 处理确认预订
    if (lowerMessage.includes('确认') || lowerMessage.includes('预订') || lowerMessage.includes('是的') || lowerMessage.includes('好的')) {
      confirmBooking()
    } else if (lowerMessage.includes('取消') || lowerMessage.includes('不') || lowerMessage.includes('重新')) {
      // 取消预订，重置状态
      resetBookingState()

      fallbackMessages.value.push({
        type: 'ai',
        content: `❌ 预订已取消。\n如需重新预订，请明确说出"预订XXX号房"。`,
        timestamp: new Date().toLocaleTimeString()
      })
      scrollFallbackToBottom()
    } else {
      fallbackMessages.value.push({
        type: 'ai',
        content: `请回复"确认预订"来完成预订，或"取消"来重新选择。`,
        timestamp: new Date().toLocaleTimeString()
      })
      scrollFallbackToBottom()
    }
  }
}

// scrollToBottom函数已不再需要，因为Dify聊天助手自己处理滚动

// 预订房间
const bookRoom = (room: any) => {
  if (auth.isAuthenticated) {
    // 创建订单
    createOrder(room)
  } else {
    router.push('/login')
  }
}

// 选择房间
const selectRoom = (room: any) => {
  // 可以添加房间详情查看逻辑
  console.log('选择房间:', room)
}

// 创建订单
const createOrder = (room: any) => {
  const today = new Date()
  const checkIn = new Date(today.getTime() + 24 * 60 * 60 * 1000) // 明天
  const checkOut = new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000) // 后天
  const nights = 2

  const order = {
    orderNumber: 'ORD' + Date.now(),
    roomId: room.id,
    roomName: room.name,
    roomCode: room.code,
    roomPrice: room.price,
    checkInDate: formatDate(checkIn),
    checkOutDate: formatDate(checkOut),
    nights: nights,
    totalAmount: room.price * nights,
    status: 'pending', // pending, confirmed, cancelled
    createTime: new Date().toLocaleString()
  }

  currentOrder.value = order

  // 添加AI消息确认预订
  chatMessages.value.push({
    type: 'ai',
    content: `✅ 预订成功！\n\n订单号：${order.orderNumber}\n房间：${order.roomName} (${order.roomCode})\n入住：${order.checkInDate}\n退房：${order.checkOutDate}\n总金额：¥${order.totalAmount}`,
    timestamp: new Date().toLocaleTimeString()
  })

  // AI表情变化
  changeExpression('excited')
  setTimeout(() => changeExpression('happy'), 3000)
}

// 取消订单
const cancelOrder = () => {
  if (currentOrder.value) {
    currentOrder.value.status = 'cancelled'

    chatMessages.value.push({
      type: 'ai',
      content: `❌ 订单已取消\n\n订单号：${currentOrder.value.orderNumber}\n如需重新预订，请告诉我您的需求。`,
      timestamp: new Date().toLocaleTimeString()
    })

    setTimeout(() => {
      currentOrder.value = null
    }, 3000)

    changeExpression('sad')
    setTimeout(() => changeExpression('happy'), 2000)
  }
}

// 修改订单
const modifyOrder = () => {
  if (currentOrder.value) {
    chatMessages.value.push({
      type: 'ai',
      content: `🔄 订单修改功能开发中...\n\n如需修改订单，请联系客服或重新预订。`,
      timestamp: new Date().toLocaleTimeString()
    })
  }
}

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const statusMap = {
    'pending': '待确认',
    'confirmed': '已确认',
    'cancelled': '已取消',
    'completed': '已完成'
  }
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (date: Date) => {
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
}
</script>

<style scoped>
/* 基础布局 */
.ai-room-selection {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.main-content {
  padding-top: 80px;
}

.container {
  max-width: 1600px;  /* 增大最大宽度：从1400px到1600px */
  margin: 0 auto;
  padding: 0 30px;  /* 增大内边距：从20px到30px */
}

/* 上下分层布局 */
.layout-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
  min-height: 1600px;  /* 再次增大最小高度：从1400px到1600px */
}

/* 上半部分：AI对话区域 */
.top-section {
  display: flex;
  gap: 30px;
  height: 1150px;  /* 进一步增大高度：从1120px到1150px */
}

/* 下半部分：房间列表区域 */
.bottom-section {
  flex: 1;
  min-height: 250px;  /* 进一步缩小最小高度：从300px到250px */
}

.left-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 1200px;  /* 增大总高度以容纳三个区域 */
}

.right-panel {
  width: 600px;  /* 保持大宽度 */
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  height: 1200px;  /* 同步增大高度，与左侧对齐 */
}

/* 左侧第一层：AI头像区域 */
.ai-header-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;  /* 适中的内边距 */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 30px;  /* 适中的间距 */
  height: 160px;  /* 适中的高度，为其他区域留出空间 */
  flex-shrink: 0;
}

.ai-avatar {
  flex-shrink: 0;
}

.ai-character-container {
  position: relative;
  width: 120px;  /* 增大：从80px到120px */
  height: 120px; /* 增大：从80px到120px */
  cursor: pointer;
  transition: transform 0.3s ease;
}

.ai-character {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #fff;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.ai-character.thinking {
  animation: thinking 1.5s ease-in-out infinite;
}

.ai-character.speaking {
  animation: speaking 0.8s ease-in-out infinite;
  box-shadow: 0 0 25px rgba(255,193,7,0.8);
}

.ai-expression-overlay {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 30px;
  height: 30px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  z-index: 2;
  font-size: 16px;
}

.ai-speaking-indicator {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 3px;
  z-index: 2;
}

.sound-wave {
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  animation: soundWave 1s ease-in-out infinite;
}

.sound-wave:nth-child(2) {
  animation-delay: 0.2s;
}

.sound-wave:nth-child(3) {
  animation-delay: 0.4s;
}

.ai-intro {
  flex: 1;
}

.ai-title {
  font-size: 2.2rem;  /* 增大：从1.6rem到2.2rem */
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 12px 0;  /* 增大间距 */
}

.ai-subtitle {
  color: #6c757d;
  font-size: 1.1rem;  /* 增大：从0.95rem到1.1rem */
  margin: 0 0 20px 0;  /* 增大间距 */
  line-height: 1.4;
}

.ai-controls {
  display: flex;
  gap: 10px;
}

.ai-voice-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 20px;
  color: #6c757d;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
}

.ai-voice-toggle:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}

.ai-voice-toggle.active {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

/* 左侧第二层：AI推荐房间列表 */
.ai-recommendations-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 25px;  /* 合理内边距 */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  height: 650px;  /* 调整高度，为订单区域留出空间 */
  flex-shrink: 0;  /* 不允许收缩 */
  overflow: hidden;  /* 让内部列表处理滚动 */
  display: flex;
  flex-direction: column;  /* 确保垂直布局 */
}

.section-header {
  margin-bottom: 20px;  /* 减少间距为列表留出更多空间 */
  flex-shrink: 0;  /* 标题区域不收缩 */
}

.recommendation-subtitle {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 8px 0 0 0;
  font-style: italic;
}

.section-header h3 {
  font-size: 1.3rem;  /* 减小标题字体 */
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 8px 0;  /* 减小间距 */
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 12px;  /* 减小卡片间距，让卡片更紧凑 */
  flex: 1;  /* 占用剩余空间 */
  overflow-y: auto;  /* 允许滚动 */
  padding-right: 8px;  /* 为滚动条留出更多空间 */
  padding-bottom: 15px;  /* 减小底部空间 */
}

.recommendation-card {
  display: flex;
  align-items: flex-start;  /* 顶部对齐 */
  gap: 12px;  /* 减小间距 */
  background: white;
  border-radius: 12px;  /* 减小圆角 */
  padding: 12px;  /* 大幅减小内边距 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);  /* 减小阴影 */
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;  /* 减小边框 */
  min-height: 80px;  /* 大幅减小最小高度 */
  width: 100%;  /* 确保卡片占满宽度 */
  box-sizing: border-box;  /* 包含padding和border在内的盒模型 */
  margin-bottom: 0;  /* 确保没有额外的margin */
  position: relative;  /* 确保定位正确 */
}

/* 移除hover效果，因为卡片不再可点击 */

.room-image {
  width: 60px;   /* 大幅减小图片尺寸 */
  height: 60px;  /* 大幅减小图片尺寸 */
  flex-shrink: 0;
  position: relative;
}

.room-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

/* 房间状态标签已删除，保持图片简洁 */

.room-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 4px;  /* 减小间距 */
  min-width: 0;  /* 允许内容收缩 */
  overflow: hidden;  /* 防止内容溢出 */
}

.room-name {
  font-size: 0.9rem;  /* 减小字体 */
  font-weight: bold;
  color: #2c3e50;
  margin: 0;
  line-height: 1.2;
}

.room-code {
  color: #6c757d;
  font-size: 0.75rem;  /* 减小字体 */
  margin: 0;
}

.room-category {
  color: #007bff;
  font-size: 0.75rem;  /* 减小字体 */
  font-weight: 500;
  margin: 0;
}

/* 房间价格样式 */
.room-price {
  color: #e74c3c;
  font-size: 0.8rem;  /* 减小字体 */
  font-weight: bold;
  margin: 0;
}

/* 房间状态信息样式 */
.room-status-info {
  margin-top: 2px;  /* 进一步减少上边距 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.availability-status {
  font-size: 0.7rem;  /* 减小字体 */
  font-weight: 500;
  padding: 2px 6px;  /* 减小内边距 */
  border-radius: 8px;  /* 减小圆角 */
  display: inline-block;
}

.vr-link {
  font-size: 0.7rem;
  color: #28a745;
  text-decoration: none;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 6px;
  transition: all 0.3s ease;
  border: 1px solid #28a745;
  background: transparent;
}

.vr-link:hover {
  background: #28a745;
  color: white;
  text-decoration: none;
}

.availability-status.available {
  background: #d4edda;
  color: #155724;
}

.availability-status.booked {
  background: #f8d7da;
  color: #721c24;
}

/* AI推荐区域不显示操作按钮 */
.room-actions {
  display: none;  /* 隐藏AI推荐区域的按钮 */
}

.book-btn, .detail-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.book-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  flex: 1;
}

.book-btn:hover {
  background: linear-gradient(135deg, #218838, #1ba085);
  transform: translateY(-1px);
}

.detail-btn {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.detail-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.ai-reason {
  margin-top: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
  border-radius: 8px;
  font-size: 12px;
  color: #1976d2;
  font-weight: 500;
  word-wrap: break-word;  /* 允许长文本换行 */
  line-height: 1.4;  /* 增加行高提高可读性 */
}

/* 房间信息头部布局 */
.room-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px;  /* 减少下边距：从6px到4px */
}

.room-header .room-name {
  flex: 1;
  margin: 0;
  margin-right: 10px;
}

.room-header .room-price {
  color: #e74c3c;
  font-size: 0.9rem;
  font-weight: bold;
  white-space: nowrap;
}

/* 房间元信息布局 */
.room-meta {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;  /* 减少下边距：从8px到4px */
  flex-wrap: wrap;
}

.room-meta span {
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 4px;
  background: #f8f9fa;
  color: #6c757d;
}

.room-meta .room-code {
  background: #e3f2fd;
  color: #1976d2;
}

.room-meta .room-category {
  background: #e8f5e8;
  color: #2e7d32;
}

.room-meta .room-rating {
  background: #fff3e0;
  color: #f57c00;
}

.room-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.room-price {
  font-size: 1.1rem;
  font-weight: bold;
  color: #e74c3c;
}

.room-rating {
  color: #ffc107;
  font-size: 0.9rem;
}

.book-btn {
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
}

.book-btn:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.book-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.no-recommendations {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 150px;  /* 适度减少高度：从200px到150px */
}

.empty-state {
  text-align: center;
  color: #6c757d;
}

.empty-state i {
  font-size: 2rem;
  margin-bottom: 10px;
  opacity: 0.5;
}

.empty-state p {
  font-size: 0.95rem;
  margin: 0;
}

.empty-subtitle {
  font-size: 0.85rem !important;
  color: #999 !important;
  margin-top: 5px !important;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 15px;
  opacity: 0.5;
}

.empty-state p {
  margin: 0;
  font-size: 1rem;
}

/* 左侧第三层：订单详情 */
.order-details-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;  /* 适中的内边距 */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  height: 330px;  /* 调整高度，确保总高度平衡 */
  flex-shrink: 0;
  overflow-y: auto;
}

.order-content {
  max-height: 300px;
  overflow-y: auto;
}

.no-order {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 150px;
}

.order-info {
  background: white;
  border-radius: 15px;
  padding: 25px;  /* 增大内边距：从20px到25px */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;  /* 增大间距：从20px到25px */
  padding-bottom: 20px;  /* 增大间距：从15px到20px */
  border-bottom: 1px solid #e9ecef;
}

.order-header h4 {
  font-size: 1.3rem;  /* 增大字体：从1.1rem到1.3rem */
  font-weight: bold;
  color: #2c3e50;
  margin: 0;
}

.order-status {
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.order-status.pending {
  background: #fff3cd;
  color: #856404;
}

.order-status.confirmed {
  background: #d4edda;
  color: #155724;
}

.order-status.cancelled {
  background: #f8d7da;
  color: #721c24;
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.detail-item label {
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 500;
}

.detail-item span {
  color: #2c3e50;
  font-weight: 500;
}

.detail-item.total {
  border-top: 1px solid #e9ecef;
  padding-top: 15px;
  margin-top: 10px;
}

.total-price {
  font-size: 1.2rem;
  font-weight: bold;
  color: #e74c3c;
}

.order-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.cancel-btn, .modify-btn {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #dc3545;
  color: white;
}

.cancel-btn:hover {
  background: #c82333;
}

.modify-btn {
  background: #ffc107;
  color: #212529;
}

.modify-btn:hover {
  background: #e0a800;
}

/* 右侧Dify聊天区域 - 为Dify对话框提供定位基准 */
.right-panel .dify-chat-container {
  background: rgba(255, 255, 255, 0.95);  /* 与左侧一致的透明度 */
  backdrop-filter: blur(10px);  /* 与左侧一致的模糊效果 */
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);  /* 与左侧一致的阴影 */
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;  /* 为Dify绝对定位提供基准 */
}

.dify-chat-header {
  padding: 25px;  /* 与左侧AI推荐区域一致的内边距 */
  border-bottom: 1px solid #e9ecef;  /* 与左侧一致的边框 */
  flex-shrink: 0;
  position: relative;
  z-index: 5;  /* 低于Dify对话框的层级 */
}

.dify-chat-header h3 {
  font-size: 1.4rem;  /* 与左侧标题大小一致 */
  font-weight: bold;
  color: #2c3e50;
  margin: 0 0 15px 0;  /* 与左侧间距一致 */
}

.dify-chat-header p {
  color: #6c757d;
  font-size: 0.9rem;  /* 与左侧描述文字大小一致 */
  margin: 0;
  line-height: 1.5;
}

/* AI头部内容布局 */
.ai-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

/* Dify状态指示器 */
.dify-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.8rem;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(248, 249, 250, 0.8);
}

.dify-status.connected {
  color: #28a745;
  background: rgba(40, 167, 69, 0.1);
}

.dify-status.disconnected {
  color: #6c757d;
  background: rgba(108, 117, 125, 0.1);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.dify-chat-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
  padding: 0;  /* 无内边距 */
  background: transparent;  /* 透明背景 */
}

/* 加载提示样式 - 与左侧保持一致 */
.dify-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: transparent;  /* 透明背景，与容器一致 */
  padding: 25px;  /* 与左侧一致的内边距 */
}

.loading-content {
  text-align: center;
  color: #6c757d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 备用聊天界面样式 - 与左侧保持一致 */
.fallback-chat {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;  /* 透明背景，与容器背景一致 */
  padding: 25px;  /* 与左侧一致的内边距 */
}

.fallback-chat .chat-messages {
  flex: 1;
  padding: 0;  /* 移除内边距，因为父容器已有 */
  overflow-y: auto;
  max-height: calc(100% - 80px);
  background: transparent;
}

.fallback-chat .message {
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
}

.fallback-chat .message.user {
  align-items: flex-end;
}

.fallback-chat .message.ai {
  align-items: flex-start;
}

.fallback-chat .message-content {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.fallback-chat .message.user .message-content {
  background: #007bff;
  color: white;
  border-bottom-right-radius: 6px;
}

.fallback-chat .message.ai .message-content {
  background: white;
  color: #333;
  border: 1px solid #e9ecef;
  border-bottom-left-radius: 6px;
}

.fallback-chat .message-text {
  line-height: 1.4;
  white-space: pre-wrap;
}

.fallback-chat .message-time {
  font-size: 0.7rem;
  opacity: 0.7;
  margin-top: 5px;
  text-align: right;
}

.fallback-chat .message.ai .message-time {
  text-align: left;
}

.fallback-chat .chat-input-area {
  padding: 15px 0;  /* 减少水平内边距，因为父容器已有 */
  border-top: 1px solid #e9ecef;
  background: transparent;  /* 透明背景 */
}

.fallback-chat .input-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.fallback-chat .chat-input {
  flex: 1;
  padding: 10px 15px;
  border: 2px solid #e9ecef;
  border-radius: 20px;
  font-size: 0.9rem;
  outline: none;
  transition: all 0.3s ease;
}

.fallback-chat .chat-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.fallback-chat .send-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.fallback-chat .send-btn:hover {
  background: #0056b3;
  transform: scale(1.05);
}

.fallback-chat .send-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

/* 测试按钮样式 */
.test-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  margin-right: 8px;
  transition: background 0.3s ease;
}

.test-btn:hover {
  background: #218838;
}

.test-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* Dify聊天助手自定义样式 - 完全填充 */
.dify-chat-wrapper iframe {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  border-radius: 20px !important;  /* 与容器圆角一致 */
  background: transparent !important;
  box-shadow: none !important;
  z-index: 10 !important;
}

/* 隐藏Dify默认的聊天气泡按钮 */
#dify-chatbot-bubble-button {
  display: none !important;
}

/* 自定义Dify聊天窗口样式 - 完全覆盖右侧区域 */
#dify-chatbot-bubble-window {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  bottom: 0 !important;
  right: 0 !important;
  width: 100% !important;
  height: 100% !important;
  max-width: none !important;
  max-height: none !important;
  border: none !important;
  border-radius: 20px !important;  /* 与容器圆角一致 */
  box-shadow: none !important;
  background: transparent !important;
  overflow: hidden !important;
  z-index: 10 !important;  /* 确保在最上层 */
}



/* 动画效果 */
@keyframes thinking {
  0%, 100% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.02);
    filter: brightness(0.95);
  }
}

@keyframes speaking {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
  50% {
    transform: scale(1.03);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.8);
  }
}

@keyframes soundWave {
  0%, 100% {
    height: 10px;
    opacity: 0.7;
  }
  50% {
    height: 20px;
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

/* 全部房间列表样式 */
.all-rooms-container {
  margin-top: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.all-rooms-header {
  padding: 20px 25px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.all-rooms-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: bold;
}

.rooms-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.view-toggle {
  display: flex;
  background: #f8f9fa;
  border-radius: 6px;
  overflow: hidden;
}

.toggle-btn {
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-btn.active {
  background: #007bff;
  color: white;
}

.toggle-btn:hover:not(.active) {
  background: #e9ecef;
}

.rooms-filter select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  color: #495057;
  font-size: 0.9rem;
  cursor: pointer;
}

.rooms-content {
  padding: 20px;
}

.rooms-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6c757d;
}

.no-rooms {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.empty-state {
  text-align: center;
  color: #6c757d;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 15px;
  opacity: 0.5;
}

.rooms-list {
  display: grid;
  gap: 20px;
}

.rooms-list.grid {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));  /* 缩小最小宽度：从300px到250px */
}

.rooms-list.list {
  grid-template-columns: 1fr;
}

.room-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.room-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #007bff;
}

.room-card.unavailable {
  opacity: 0.7;
  cursor: not-allowed;
}

.room-card.unavailable:hover {
  transform: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-color: transparent;
}

.room-image {
  position: relative;
  height: 160px;  /* 缩小高度：从200px到160px */
  overflow: hidden;
}

.room-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.room-card:hover .room-image img {
  transform: scale(1.05);
}

.room-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  color: white;
}

.room-status.available {
  background: #28a745;
}

.room-status.booked {
  background: #dc3545;
}

.room-info {
  padding: 15px;  /* 缩小内边距：从20px到15px */
}

.room-name {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: bold;
}

.room-code {
  margin: 0 0 8px 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.room-description {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 0.9rem;
  line-height: 1.4;
}

.room-details {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.room-details span {
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 0.8rem;
  color: #495057;
}

.room-rating {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 12px;
  color: #ffc107;
}

.room-rating span {
  color: #495057;
  font-weight: bold;
}

.room-price {
  display: flex;
  align-items: baseline;
  gap: 5px;
  margin-bottom: 15px;
}

.price {
  font-size: 1.4rem;
  font-weight: bold;
  color: #007bff;
}

.unit {
  color: #6c757d;
  font-size: 0.9rem;
}

.book-btn {
  width: 100%;
  padding: 10px;
  border: none;
  border-radius: 6px;
  background: #007bff;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.book-btn:hover:not(.disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.book-btn.disabled {
  background: #6c757d;
  cursor: not-allowed;
}

/* 列表视图特殊样式 */
.rooms-list.list .room-card {
  display: flex;
  align-items: stretch;
}

.rooms-list.list .room-image {
  width: 250px;
  height: 180px;
  flex-shrink: 0;
}

.rooms-list.list .room-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .layout-container {
    gap: 20px;
  }

  .top-section {
    flex-direction: column;
    height: auto;
    gap: 20px;
  }

  .left-panel {
    width: 100%;
    gap: 15px;
  }

  .right-panel {
    width: 100%;
    height: 400px;
  }

  .bottom-section {
    min-height: 500px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .main-content {
    padding-top: 70px;
  }

  .ai-header-section {
    padding: 20px;
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .ai-character-container {
    width: 100px;
    height: 100px;
  }

  .ai-title {
    font-size: 1.4rem;
  }

  .ai-recommendations-section,
  .order-details-section {
    padding: 20px;
  }

  .recommendation-card {
    flex-direction: column;
    text-align: center;
  }

  .room-image {
    width: 100%;
    height: 120px;
    align-self: center;
  }

  .room-details {
    justify-content: center;
    gap: 20px;
  }

  .right-panel .dify-chat-container {
    height: 400px;
  }

  .dify-chat-wrapper {
    height: calc(100% - 100px);
  }

  /* 房间列表移动端优化 */
  .rooms-list.grid {
    grid-template-columns: 1fr;
  }

  .rooms-list.list .room-card {
    flex-direction: column;
  }

  .rooms-list.list .room-image {
    width: 100%;
    height: 200px;
  }

  .rooms-controls {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .all-rooms-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar,
.recommendations-list::-webkit-scrollbar,
.order-content::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track,
.recommendations-list::-webkit-scrollbar-track,
.order-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb,
.recommendations-list::-webkit-scrollbar-thumb,
.order-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover,
.recommendations-list::-webkit-scrollbar-thumb:hover,
.order-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 额外的Dify聊天助手样式 */
.dify-chat-wrapper * {
  box-sizing: border-box;
}

/* 确保Dify聊天助手在容器内正确显示 */
.dify-chat-wrapper > div {
  width: 100% !important;
  height: 100% !important;
  border-radius: 0 0 20px 20px !important;
  overflow: hidden !important;
}

/* Dify聊天助手内部元素优化 */
.dify-chat-wrapper * {
  box-sizing: border-box !important;
}

/* Dify聊天输入框区域优化 */
.dify-chat-wrapper .dify-chat-input {
  border-radius: 0 0 20px 20px !important;
  border: none !important;
  background: rgba(248, 249, 250, 0.8) !important;
}

/* Dify聊天消息区域优化 */
.dify-chat-wrapper .dify-chat-messages {
  padding: 15px !important;
  background: transparent !important;
}

/* Dify聊天助手完全嵌入样式 - 覆盖整个右侧区域 */
#dify-chatbot-bubble-window > div {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  border-radius: 20px !important;  /* 与容器圆角一致 */
  background: transparent !important;
  box-shadow: none !important;
  z-index: 10 !important;
}

/* 确保Dify对话框内的所有元素都正确显示 */
#dify-chatbot-bubble-window * {
  border-radius: inherit !important;
}

/* Dify对话框头部样式 */
#dify-chatbot-bubble-window .dify-chat-header {
  border-radius: 20px 20px 0 0 !important;
}

/* Dify对话框底部样式 */
#dify-chatbot-bubble-window .dify-chat-input {
  border-radius: 0 0 20px 20px !important;
}

/* 移动端Dify聊天助手优化 */
@media (max-width: 480px) {
  .dify-chat-header {
    padding: 15px;
  }

  .dify-chat-header h3 {
    font-size: 1.2rem;
  }

  .dify-chat-wrapper {
    height: calc(100% - 70px);
  }
}
</style>
