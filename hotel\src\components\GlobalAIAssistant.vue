<template>
  <div class="global-ai-assistant">
    <!-- 2D蘑菇角色容器 -->
    <div
      ref="characterContainer"
      class="character-container"
      :class="{
        'expanded': isExpanded,
        'thinking': isThinking,
        'speaking': isSpeaking,
        'listening': isListening
      }"
      @click="handleCharacterClick"
    >
      <!-- 2D角色图片 -->
      <div class="character-image-wrapper">
        <img
          :src="aiImageUrl"
          alt="AI助手"
          class="character-image"
          @error="handleImageError"
        />
        <!-- 备用角色设计 -->
        <div v-if="imageError" class="fallback-character">
          <div class="character-body">
            <div class="character-head">
              <div class="character-face">
                <div class="eyes">
                  <span class="eye">👁️</span>
                  <span class="eye">👁️</span>
                </div>
                <div class="mouth">😊</div>
              </div>
            </div>
            <div class="character-stem">🌱</div>
          </div>
        </div>
      </div>

      <!-- 状态动画效果 -->
      <div class="status-effects">
        <div v-if="isThinking" class="thinking-effect">
          <div class="thinking-bubble">
            <span>💭</span>
          </div>
        </div>
        <div v-if="isSpeaking" class="speaking-effect">
          <div class="sound-waves">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <div class="speaking-text">少年音色播放中...</div>
        </div>
        <div v-if="isListening" class="listening-effect">
          <div class="pulse-ring"></div>
        </div>
      </div>

      <!-- 交互提示 -->
      <div v-if="!isExpanded" class="interaction-hint">
        <div class="hint-bubble">
          <span>🍄 点击我开始智能对话</span>
          <div class="hint-arrow"></div>
        </div>
      </div>
    </div>

    <!-- AI对话界面 -->
    <div v-if="isExpanded" class="ai-chat-interface">
      <div class="chat-header">
        <div class="character-info">
          <div class="character-avatar-mini">
            <img :src="aiImageUrl" alt="AI助手" />
          </div>
          <div class="character-details">
            <h3>🍄 小蘑菇AI助手</h3>
            <p class="status">{{ connectionStatus }}</p>
          </div>
        </div>
        <div class="chat-controls">
          <button @click="toggleVoice" :class="['control-btn', 'voice-control', { active: voiceEnabled }]" title="切换语音回复">
            <i :class="voiceEnabled ? 'fas fa-volume-up' : 'fas fa-volume-mute'"></i>
          </button>
          <button @click="toggleVoiceCall" :class="['control-btn', 'voice-call-control', { active: isVoiceCallActive }]" title="实时语音通话">
            <span class="voice-call-icon">{{ isVoiceCallActive ? '📵' : '📞' }}</span>
            <!-- 语音通话提示 -->
            <div v-if="showVoiceCallTip && !isVoiceCallActive" class="voice-call-tip">
              <span>🎤 新功能</span>
              <button @click.stop="showVoiceCallTip = false" class="tip-close">×</button>
            </div>
          </button>
          <button @click="minimizeChat" class="control-btn minimize">
            <i class="fas fa-minus"></i>
          </button>
        </div>
      </div>

      <div class="chat-messages" ref="chatMessages">
        <!-- 欢迎消息和语音通话区域 -->
        <div v-if="messages.length === 0" class="welcome-message">
          <div class="welcome-content">
            <div class="ai-avatar">
              <img :src="aiImageUrl" alt="AI助手" />
            </div>
            <h3>您好！我是普普1.0 🍄</h3>
            <p>我是您的专属AI助手，可以为您提供专业的民宿服务咨询</p>

            <!-- 语音通话启动区域 -->
            <div class="voice-call-section">
              <div class="voice-call-card">
                <div class="voice-call-icon">🎤</div>
                <div class="voice-call-info">
                  <h4>体验实时语音通话</h4>
                  <p>与AI进行自然的语音对话，更便捷的交流方式</p>
                </div>
                <button
                  @click="startVoiceCall"
                  :class="['voice-call-btn', { 'active': isVoiceCallActive }]"
                  :disabled="isVoiceCallActive"
                >
                  <span class="voice-call-icon">{{ isVoiceCallActive ? '📵' : '📞' }}</span>
                  <span>{{ isVoiceCallActive ? '结束通话' : '开始语音通话' }}</span>
                </button>
              </div>
            </div>

            <p class="welcome-tip">您可以直接输入问题，或点击下方的建议开始对话</p>
          </div>
        </div>

        <div
          v-for="message in messages"
          :key="message.id"
          class="message"
          :class="{ 'user': message.type === 'user', 'ai': message.type === 'ai' }"
        >
          <div class="message-content">
            <div class="message-text" v-html="formatMessage(message.text)"></div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>
        
        <!-- AI思考指示器 -->
        <div v-if="isThinking" class="message ai thinking-message">
          <div class="message-content">
            <div class="thinking-animation">
              <span>小蘑菇正在思考</span>
              <div class="dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快捷导航 -->
      <div v-if="showNavigation" class="quick-navigation">
        <h4>🧭 快捷导航：</h4>
        <div class="navigation-grid">
          <button 
            v-for="nav in navigationOptions"
            :key="nav.id"
            @click="navigateToPage(nav.route, nav.text)"
            class="nav-btn"
            :class="{ 'current': isCurrentPage(nav.route) }"
          >
            <i :class="nav.icon"></i>
            {{ nav.text }}
          </button>
        </div>
      </div>

      <!-- 智能建议 -->
      <div v-if="showSuggestions" class="smart-suggestions">
        <h4>💡 智能建议：</h4>
        <div class="suggestions-grid">
          <button 
            v-for="suggestion in currentSuggestions"
            :key="suggestion.id"
            @click="sendSuggestion(suggestion.text)"
            class="suggestion-btn"
          >
            {{ suggestion.text }}
          </button>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input-area">
        <div class="input-container">
          <input 
            v-model="inputText"
            @keypress.enter="sendMessage"
            @input="handleInputChange"
            :disabled="isProcessing"
            placeholder="请输入您的问题或说'去首页'、'查看订单'等..."
            class="message-input"
            ref="messageInput"
          >
          <button
            @click="toggleVoiceInput"
            :class="{ 'active': isListening, 'disabled': isProcessing }"
            class="voice-btn"
            title="语音输入"
          >
            <i class="fas fa-microphone"></i>
          </button>
          <button
            @click="toggleVoiceCall"
            :class="['voice-call-quick-btn', { 'active': isVoiceCallActive }]"
            title="实时语音通话"
          >
            <span class="voice-call-icon">{{ isVoiceCallActive ? '📵' : '📞' }}</span>
          </button>
          <button
            @click="sendMessage"
            :disabled="!inputText.trim() || isProcessing"
            class="send-btn"
          >
            <i class="fas fa-paper-plane"></i>
          </button>
        </div>
        
        <!-- 语音输入状态 -->
        <div v-if="isListening" class="voice-status">
          <div class="voice-animation">
            <div class="voice-circle"></div>
            <div class="voice-circle"></div>
            <div class="voice-circle"></div>
          </div>
          <span>🎤 正在聆听您的声音...</span>
        </div>
      </div>
    </div>

    <!-- 实时语音通话界面 -->
    <div v-if="showVoiceCallInterface" class="voice-call-overlay">
      <div class="voice-call-container">
        <div class="voice-call-header">
          <h3>🎤 实时语音通话</h3>
          <button @click="endVoiceCall" class="close-call-btn">
            <span class="close-icon">✕</span>
          </button>
        </div>

        <div class="voice-call-content">
          <!-- AI角色显示 -->
          <div class="call-avatar-section">
            <div class="call-avatar-container" :class="{ 'speaking': isSpeaking, 'listening': isListening }">
              <img :src="aiImageUrl" alt="AI助手" class="call-avatar" />
              <div v-if="isSpeaking" class="speaking-indicator">
                <div class="sound-wave"></div>
                <div class="sound-wave"></div>
                <div class="sound-wave"></div>
              </div>
              <div v-if="isListening" class="listening-indicator">
                <div class="pulse-ring"></div>
              </div>
            </div>
            <p class="call-status">
              <span v-if="isSpeaking">🗣️ AI正在说话...</span>
              <span v-else-if="isListening">🎤 正在聆听您的声音...</span>
              <span v-else>💬 请开始说话</span>
            </p>
          </div>

          <!-- 通话控制 -->
          <div class="call-controls">
            <button
              @click="toggleVoiceInput"
              :class="['call-control-btn', 'mic-btn', { 'active': isListening }]"
              title="切换麦克风"
            >
              <span class="control-icon">{{ isListening ? '🎤' : '🔇' }}</span>
            </button>

            <button
              @click="toggleVoice"
              :class="['call-control-btn', 'speaker-btn', { 'active': voiceEnabled }]"
              title="切换扬声器"
            >
              <span class="control-icon">{{ voiceEnabled ? '🔊' : '🔈' }}</span>
            </button>

            <button
              @click="endVoiceCall"
              class="call-control-btn end-call-btn"
              title="结束通话"
            >
              <span class="control-icon">📵</span>
            </button>
          </div>

          <!-- 实时对话显示 -->
          <div class="real-time-conversation" v-if="messages.length > 0">
            <div class="latest-messages">
              <div
                v-for="message in messages.slice(-3)"
                :key="message.id"
                :class="['message-bubble', message.type]"
              >
                <span class="message-text">{{ message.text }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, watch, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { difyChatService } from '../services/DifyChatService'
// 路由和状态管理
const router = useRouter()
const route = useRoute()
const auth = useAuthStore()

// 响应式数据
const threejsContainer = ref<HTMLElement>()
const chatMessages = ref<HTMLElement>()
const messageInput = ref<HTMLInputElement>()

const isExpanded = ref(false)
const isListening = ref(false)
const isThinking = ref(false)
const isSpeaking = ref(false)
const isProcessing = ref(false)
const showSuggestions = ref(true)
const showNavigation = ref(true)
const inputText = ref('')
const connectionStatus = ref('在线服务中')
const imageError = ref(false)

// AI角色图片URL - 使用茶茶正面IP形象
const aiImageUrl = ref('/src/assets/images/IP形象/茶茶正面.png')

// 语音合成相关
const voiceEnabled = ref(false) // 默认关闭语音
const voiceType = ref('youth') // 'youth' | 'female' | 'male' - 少年音色
let speechSynthesis: any = null
let youthVoice: any = null
let femaleVoice: any = null
let maleVoice: any = null

// 实时语音通话相关
const isVoiceCallActive = ref(false)
const showVoiceCallInterface = ref(false)
const showVoiceCallTip = ref(true) // 显示语音通话提示

// 消息数据
const messages = ref<any[]>([
  {
    id: 1,
    type: 'ai',
    text: '🍄 您好！我是小蘑菇，您的专属AI助手！我可以帮您导航到不同页面，回答问题，还能协助您完成各种操作。试试说"去首页"或"查看我的订单"吧！',
    timestamp: new Date()
  }
])

// 导航选项
const navigationOptions = computed(() => {
  const baseOptions = [
    { id: 1, text: '🏠 首页', route: '/', icon: 'fas fa-home' },
    { id: 2, text: '🤖 AI选房', route: '/ai-rooms', icon: 'fas fa-robot' },
    { id: 3, text: '📞 联系我们', route: '/contact', icon: 'fas fa-phone' }
  ]
  
  // 如果用户已登录，添加更多选项
  if (auth.isAuthenticated) {
    baseOptions.push(
      { id: 4, text: '📋 我的订单', route: '/orders', icon: 'fas fa-clipboard-list' },
      { id: 5, text: '👤 个人信息', route: '/profile', icon: 'fas fa-user' },
      { id: 6, text: '🚪 退出登录', route: '/logout', icon: 'fas fa-right-from-bracket' }
    )
  } else {
    baseOptions.push(
      { id: 4, text: '🔑 登录', route: '/login', icon: 'fas fa-right-to-bracket' },
      { id: 5, text: '📝 注册', route: '/register', icon: 'fas fa-user-plus' }
    )
  }
  
  return baseOptions
})

// 智能建议
const currentSuggestions = ref([
  { id: 1, text: '🏡 推荐适合我的民宿' },
  { id: 2, text: '🍄 蘑菇采摘最佳时间' },
  { id: 3, text: '🍵 普洱茶品鉴体验' },
  { id: 4, text: '🌲 生态徒步路线' }
])

// 检查是否为当前页面
const isCurrentPage = (routePath: string): boolean => {
  if (routePath === '/logout') return false
  return route.path === routePath
}

// 导航到指定页面
const navigateToPage = async (routePath: string, pageName: string) => {
  if (routePath === '/logout') {
    // 处理退出登录
    auth.logout()
    router.push('/login')
    
    const logoutMessage = {
      id: Date.now(),
      type: 'ai',
      text: '🚪 您已成功退出登录。感谢您的使用，期待您的再次光临！',
      timestamp: new Date()
    }
    messages.value.push(logoutMessage)
  } else {
    // 普通页面导航
    router.push(routePath)
    
    const navMessage = {
      id: Date.now(),
      type: 'ai',
      text: `🧭 正在为您跳转到${pageName}...`,
      timestamp: new Date()
    }
    messages.value.push(navMessage)
  }
  
  // 滚动到底部
  await nextTick()
  scrollToBottom()
}

// 角色点击处理 - 只展开对话
const handleCharacterClick = () => {
  if (!isExpanded.value) {
    // 如果对话未展开，则打开
    expandChat()
  }
  // 如果已展开，点击角色不做任何操作（保持对话开启状态）
}

// 处理图片加载错误
const handleImageError = () => {
  imageError.value = true
}

const expandChat = () => {
  isExpanded.value = true
  showSuggestions.value = true
  showNavigation.value = true

  // 首次打开时播放欢迎语音
  if (voiceEnabled.value && messages.value.length === 0) {
    setTimeout(() => {
      const welcomeMessage = "您好！我是小蘑菇AI助手，很高兴为您服务！您可以用语音或文字与我交流，我会帮您导航、推荐房间、回答问题。"
      speakText(welcomeMessage)
    }, 1000)
  }

  nextTick(() => {
    messageInput.value?.focus()
  })
}

const minimizeChat = () => {
  isExpanded.value = false
  showSuggestions.value = false
  showNavigation.value = false
}

const closeChat = () => {
  isExpanded.value = false
  showSuggestions.value = false
  showNavigation.value = false
}

const sendMessage = async () => {
  if (!inputText.value.trim() || isProcessing.value) return

  const userMessage = {
    id: Date.now(),
    type: 'user',
    text: inputText.value,
    timestamp: new Date()
  }

  messages.value.push(userMessage)
  const messageText = inputText.value
  inputText.value = ''
  showSuggestions.value = false
  showNavigation.value = false

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  // 处理AI回复
  await processAIResponse(messageText)
}

const sendSuggestion = async (suggestionText: string) => {
  inputText.value = suggestionText
  await sendMessage()
}

const processAIResponse = async (userMessage: string) => {
  try {
    isProcessing.value = true
    isThinking.value = true

    // 检查是否为导航指令
    const navigationResult = handleNavigationCommand(userMessage)
    if (navigationResult) {
      isProcessing.value = false
      isThinking.value = false
      return
    }

    // 调用AI API
    const aiResponse = await getIntelligentAIResponse(userMessage)
    
    // 添加AI回复
    const aiMessage = {
      id: Date.now() + 1,
      type: 'ai',
      text: aiResponse,
      timestamp: new Date()
    }
    messages.value.push(aiMessage)

    // 自动语音播放回复（少年音色）
    if (voiceEnabled.value) {
      await speakText(aiResponse)
    }

    // 更新AI建议
    updateIntelligentSuggestions(analyzeUserIntent(userMessage))

    // 滚动到底部
    await nextTick()
    scrollToBottom()

  } catch (error) {
    console.error('AI回复失败:', error)
    
    const errorMessage = {
      id: Date.now() + 2,
      type: 'ai',
      text: '🍄 抱歉，我遇到了一些问题，请稍后再试。您也可以尝试重新表述您的问题。',
      timestamp: new Date()
    }
    messages.value.push(errorMessage)
    
  } finally {
    isProcessing.value = false
    isThinking.value = false
  }
}

// 处理导航指令
const handleNavigationCommand = (message: string): boolean => {
  const navigationCommands = {
    '首页': '/',
    '主页': '/',
    '房间': '/rooms',
    '民宿': '/rooms',
    '订单': '/orders',
    '我的订单': '/orders',
    '个人信息': '/profile',
    '个人中心': '/profile',
    '联系我们': '/contact',
    '联系': '/contact',
    '登录': '/login',
    '注册': '/register',
    '退出': '/logout',
    '登出': '/logout'
  }

  for (const [keyword, route] of Object.entries(navigationCommands)) {
    if (message.includes(keyword) && (message.includes('去') || message.includes('跳转') || message.includes('打开') || message.includes('查看'))) {
      const pageName = keyword
      navigateToPage(route, pageName)
      return true
    }
  }

  return false
}

// 其他辅助方法
const getIntelligentAIResponse = async (message: string): Promise<string> => {
  console.log('🤖 开始获取智能AI响应:', message)

  try {
    // 使用Dify服务获取AI回复
    console.log('📡 调用 Dify 服务...')
    const stream = await difyChatService.sendMessage(message, auth.user?.username || 'guest')
    let fullResponse = ''

    // 处理流式响应
    console.log('🔄 处理流式响应...')
    for await (const chunk of difyChatService.processStreamResponse(stream)) {
      fullResponse += chunk
      console.log('📝 累积响应长度:', fullResponse.length)
    }

    console.log('✅ Dify 完整响应:', fullResponse)

    // 只有在真正没有响应时才降级
    if (!fullResponse || fullResponse.trim() === '') {
      console.warn('⚠️ Dify服务返回空响应，降级到本地AI响应')
      const intent = analyzeUserIntent(message)
      return generateIntelligentResponse(message, intent)
    }

    // 返回 Dify 的完整响应（包含思考过程）
    return fullResponse.trim()
  } catch (error) {
    console.error('❌ Dify AI服务调用失败:', error)

    // 只有在网络错误或严重错误时才降级
    if (error instanceof TypeError && error.message.includes('fetch')) {
      console.warn('🌐 网络错误，降级到本地响应')
    } else {
      console.warn('🔧 服务错误，降级到本地响应')
    }

    const intent = analyzeUserIntent(message)
    return generateIntelligentResponse(message, intent)
  }
}

const analyzeUserIntent = (message: string): string => {
  const intents = {
    navigation: ['去', '跳转', '打开', '查看', '进入'],
    booking: ['预订', '预约', '订房', '入住', '房间', '价格'],
    activities: ['活动', '体验', '采摘', '茶艺', '徒步', '娱乐'],
    food: ['美食', '餐厅', '吃', '料理', '菜品', '特色'],
    location: ['位置', '地址', '路线', '交通', '怎么去'],
    greeting: ['你好', '您好', 'hello', 'hi']
  }

  for (const [intent, keywords] of Object.entries(intents)) {
    if (keywords.some(keyword => message.includes(keyword))) {
      return intent
    }
  }

  return 'general'
}

const generateIntelligentResponse = (message: string, intent: string): string => {
  // 只处理基本的礼貌用语，其他交给Dify处理
  if (message.includes('你好') || message.includes('您好') || message.includes('hello')) {
    return '🍄 您好！我是普洱蘑菇庄园民宿的AI助手。AI服务暂时不可用，您可以稍后重试或使用导航功能。'
  }

  if (message.includes('谢谢') || message.includes('感谢') || message.includes('thanks')) {
    return '🍄 不客气！AI服务暂时不可用，您可以稍后重试。'
  }

  if (message.includes('再见') || message.includes('拜拜') || message.includes('bye')) {
    return '🍄 再见！期待您的到来！'
  }

  // 简化的降级回复，避免硬编码详细内容
  const responses = {
    navigation: '🧭 导航功能可用，您可以说"去首页"、"查看订单"等。',
    booking: '🏡 AI服务暂时不可用，您可以点击"AI选房"或稍后重试。',
    activities: '🍄 AI服务暂时不可用，您可以查看"茶文化"页面或稍后重试。',
    food: '🍽️ AI服务暂时不可用，您可以稍后重试了解美食信息。',
    general: '🍄 AI服务暂时不可用，您可以稍后重试或使用导航功能。'
  }

  return responses[intent as keyof typeof responses] || responses.general
}

const updateIntelligentSuggestions = (intent: string) => {
  const suggestionMap = {
    navigation: [
      { id: 1, text: '🏠 返回首页' },
      { id: 2, text: '🏡 查看房间' },
      { id: 3, text: '📋 我的订单' },
      { id: 4, text: '👤 个人信息' }
    ],
    booking: [
      { id: 1, text: '📅 查看可用日期' },
      { id: 2, text: '💰 了解价格详情' },
      { id: 3, text: '🎁 查看优惠活动' },
      { id: 4, text: '📞 联系客服预订' }
    ]
  }

  currentSuggestions.value = suggestionMap[intent as keyof typeof suggestionMap] || [
    { id: 1, text: '🔍 了解更多详情' },
    { id: 2, text: '💬 继续咨询' },
    { id: 3, text: '📞 联系客服' },
    { id: 4, text: '🧭 页面导航' }
  ]

  showSuggestions.value = true
}

const toggleVoiceInput = async () => {
  if (isListening.value) {
    stopVoiceInput()
  } else {
    await startVoiceInput()
  }
}

// 语音识别相关
let recognition: any = null
let isRecognitionSupported = false

// 检查浏览器是否支持语音识别
const checkSpeechRecognition = () => {
  if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
    recognition = new SpeechRecognition()

    // 配置语音识别
    recognition.continuous = false
    recognition.interimResults = false
    recognition.lang = 'zh-CN' // 中文识别

    // 识别结果处理
    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript
      inputText.value = transcript
      isListening.value = false

      // 自动发送消息
      if (transcript.trim()) {
        sendMessage()
      }
    }

    // 识别错误处理
    recognition.onerror = (event: any) => {
      console.error('语音识别错误:', event.error)
      isListening.value = false

      let errorMessage = '语音识别失败'
      switch (event.error) {
        case 'no-speech':
          errorMessage = '未检测到语音，请重试'
          break
        case 'audio-capture':
          errorMessage = '无法访问麦克风，请检查权限'
          break
        case 'not-allowed':
          errorMessage = '麦克风权限被拒绝，请在浏览器设置中允许'
          break
        case 'network':
          errorMessage = '网络错误，请检查网络连接'
          break
      }

      // 显示错误提示
      const errorMsg = {
        id: Date.now(),
        type: 'ai',
        text: `🎤 ${errorMessage}`,
        timestamp: new Date()
      }
      messages.value.push(errorMsg)
      scrollToBottom()
    }

    // 识别结束处理
    recognition.onend = () => {
      isListening.value = false
    }

    isRecognitionSupported = true
  } else {
    isRecognitionSupported = false
    console.warn('浏览器不支持语音识别')
  }
}

const startVoiceInput = async () => {
  if (!isRecognitionSupported) {
    const errorMsg = {
      id: Date.now(),
      type: 'ai',
      text: '🎤 抱歉，您的浏览器不支持语音识别功能。建议使用Chrome、Edge或Safari浏览器。',
      timestamp: new Date()
    }
    messages.value.push(errorMsg)
    scrollToBottom()
    return
  }

  try {
    recognition.start()
    isListening.value = true

    // 添加提示消息
    const hintMsg = {
      id: Date.now(),
      type: 'ai',
      text: '🎤 正在聆听您的语音，请说出您的需求...',
      timestamp: new Date()
    }
    messages.value.push(hintMsg)
    scrollToBottom()
  } catch (error) {
    console.error('启动语音识别失败:', error)
    isListening.value = false
  }
}

const stopVoiceInput = () => {
  if (recognition) {
    recognition.stop()
  }
  isListening.value = false
}

const handleInputChange = () => {
  // 实时输入建议
}

const formatMessage = (text: string): string => {
  return text.replace(/\n/g, '<br>')
}

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const scrollToBottom = () => {
  if (chatMessages.value) {
    chatMessages.value.scrollTop = chatMessages.value.scrollHeight
  }
}

// 监听路由变化，提供AI提示
watch(() => route.path, (newPath, oldPath) => {
  // 如果从首页跳转到登录页，AI助手主动提供帮助
  if (oldPath === '/' && newPath === '/login') {
    setTimeout(() => {
      const loginHelpMessage = {
        id: Date.now(),
        type: 'ai',
        text: '🔑 我注意到您准备登录！如果您是新用户，我可以帮您快速注册。如果忘记密码，我也能协助您找回。有什么需要帮助的吗？',
        timestamp: new Date()
      }
      messages.value.push(loginHelpMessage)
      
      // 如果AI助手没有展开，自动展开
      if (!isExpanded.value) {
        expandChat()
      }
      
      // 滚动到底部
      nextTick(() => {
        scrollToBottom()
      })
    }, 1000)
  }
})

// 语音合成初始化
const initializeSpeechSynthesis = () => {
  if ('speechSynthesis' in window) {
    speechSynthesis = window.speechSynthesis

    // 等待语音列表加载完成
    const loadVoices = () => {
      const voices = speechSynthesis.getVoices()

      // 寻找适合的少年音色（中文女声，音调较高）
      youthVoice = voices.find((voice: any) =>
        voice.lang.includes('zh') &&
        (voice.name.includes('Female') ||
         voice.name.includes('女') ||
         voice.name.includes('Xiaoxiao') ||
         voice.name.includes('Xiaoyi'))
      ) || voices.find((voice: any) => voice.lang.includes('zh')) || voices[0]

      // 寻找女声
      femaleVoice = voices.find((voice: any) =>
        voice.lang.includes('zh') &&
        (voice.name.includes('Female') || voice.name.includes('女'))
      ) || youthVoice

      // 寻找男声
      maleVoice = voices.find((voice: any) =>
        voice.lang.includes('zh') &&
        (voice.name.includes('Male') || voice.name.includes('男'))
      ) || youthVoice

      console.log('语音配置:', {
        youth: youthVoice?.name || '默认',
        female: femaleVoice?.name || '默认',
        male: maleVoice?.name || '默认'
      })
    }

    // 语音列表可能需要时间加载
    if (speechSynthesis.getVoices().length > 0) {
      loadVoices()
    } else {
      speechSynthesis.onvoiceschanged = loadVoices
    }
  }
}

// 语音播放函数（少年音色）
const speakText = async (text: string) => {
  if (!voiceEnabled.value || !speechSynthesis) return false

  // 清理文本
  const cleanText = text
    .replace(/🍄|🧭|🏡|📅|🍽️|☕|🌿|🎤|🗺️/g, '') // 移除emoji
    .replace(/\*\*(.*?)\*\*/g, '$1') // 移除markdown粗体
    .replace(/\n/g, ' ') // 换行替换为空格
    .replace(/\s+/g, ' ') // 多个空格合并
    .trim()

  if (!cleanText) return false

  return new Promise((resolve) => {
    // 停止当前播放
    speechSynthesis.cancel()

    const utterance = new SpeechSynthesisUtterance(cleanText)

    // 根据音色类型设置语音参数
    let selectedVoice = youthVoice
    let rate = 1.1
    let pitch = 1.3

    switch (voiceType.value) {
      case 'youth':
        selectedVoice = youthVoice
        rate = 1.1 // 稍快的语速
        pitch = 1.3 // 较高的音调，模拟少年音色
        break
      case 'female':
        selectedVoice = femaleVoice
        rate = 1.0 // 正常语速
        pitch = 1.2 // 中等音调
        break
      case 'male':
        selectedVoice = maleVoice
        rate = 0.9 // 稍慢的语速
        pitch = 0.8 // 较低的音调
        break
    }

    if (selectedVoice) {
      utterance.voice = selectedVoice
    }
    utterance.lang = 'zh-CN'
    utterance.rate = rate
    utterance.pitch = pitch
    utterance.volume = 0.8

    utterance.onstart = () => {
      isSpeaking.value = true
    }

    utterance.onend = () => {
      isSpeaking.value = false
      resolve(true)
    }

    utterance.onerror = (event) => {
      console.error('语音播放错误:', event.error)
      isSpeaking.value = false
      resolve(false)
    }

    speechSynthesis.speak(utterance)
  })
}

// 切换语音功能
const toggleVoice = () => {
  voiceEnabled.value = !voiceEnabled.value

  if (!voiceEnabled.value && isSpeaking.value) {
    speechSynthesis?.cancel()
    isSpeaking.value = false
  }
}

// 切换实时语音通话
const toggleVoiceCall = () => {
  if (isVoiceCallActive.value) {
    // 结束语音通话
    endVoiceCall()
  } else {
    // 开始语音通话
    startVoiceCall()
  }
}

// 开始语音通话
const startVoiceCall = () => {
  // 检查浏览器支持
  if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
    const errorMsg = {
      id: Date.now(),
      type: 'ai',
      text: '🎤 抱歉，您的浏览器不支持实时语音通话功能。建议使用Chrome、Edge或Safari浏览器。',
      timestamp: new Date()
    }
    messages.value.push(errorMsg)
    scrollToBottom()
    return
  }

  if (!('speechSynthesis' in window)) {
    const errorMsg = {
      id: Date.now(),
      type: 'ai',
      text: '🔊 抱歉，您的浏览器不支持语音合成功能。',
      timestamp: new Date()
    }
    messages.value.push(errorMsg)
    scrollToBottom()
    return
  }

  isVoiceCallActive.value = true
  showVoiceCallInterface.value = true

  // 添加通话开始提示
  const startMsg = {
    id: Date.now(),
    type: 'ai',
    text: '📞 实时语音通话已开启！现在您可以直接与我进行语音对话，我会实时回复您。说话时我会自动检测并回应。',
    timestamp: new Date()
  }
  messages.value.push(startMsg)
  scrollToBottom()

  // 自动播放欢迎语音
  if (voiceEnabled.value) {
    setTimeout(() => {
      speakText('实时语音通话已开启，请开始与我对话吧！')
    }, 500)
  }
}

// 结束语音通话
const endVoiceCall = () => {
  isVoiceCallActive.value = false
  showVoiceCallInterface.value = false

  // 停止当前语音
  if (speechSynthesis && isSpeaking.value) {
    speechSynthesis.cancel()
    isSpeaking.value = false
  }

  // 停止语音识别
  if (recognition && isListening.value) {
    recognition.stop()
    isListening.value = false
  }

  // 添加通话结束提示
  const endMsg = {
    id: Date.now(),
    type: 'ai',
    text: '📞 实时语音通话已结束。您可以继续使用文字聊天或重新开启语音通话。',
    timestamp: new Date()
  }
  messages.value.push(endMsg)
  scrollToBottom()
}

// 生命周期
onMounted(() => {
  // 初始化语音识别
  checkSpeechRecognition()
  // 初始化语音合成
  initializeSpeechSynthesis()
})
</script>

<script lang="ts">
export default {
  name: 'GlobalAIAssistant'
}
</script>

<style scoped>
/* 全局AI助手容器 */
.global-ai-assistant {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 2D角色容器 */
.character-container {
  width: 140px;
  height: 140px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: center;
}

.character-container:hover {
  transform: translateY(-4px) scale(1.05);
}

.character-container.expanded {
  width: 120px;
  height: 120px;
  position: absolute;
  top: -60px;
  right: 0;
  cursor: pointer;
}

/* 2D角色图片 */
.character-image-wrapper {
  width: 120px;
  height: 120px;
  position: relative;
  animation: characterFloat 3s ease-in-out infinite;
  transition: all 0.3s ease;
  background: transparent;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.character-container.thinking .character-image-wrapper {
  animation: characterThink 2s ease-in-out infinite;
}

.character-container.speaking .character-image-wrapper {
  animation: characterSpeak 0.5s ease-in-out infinite alternate;
}

.character-container.listening .character-image-wrapper {
  animation: characterListen 1s ease-in-out infinite;
}

.character-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
  border-radius: 50%;
  background: transparent;
}

.character-container:hover .character-image {
  transform: scale(1.05);
}

/* 备用角色设计 */
.fallback-character {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f4f1de 0%, #e9c46a 100%);
  border-radius: 50%;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.character-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.character-head {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #fff8e1 0%, #f4f1de 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 8px;
}

.character-face {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.eyes {
  display: flex;
  gap: 8px;
}

.eye {
  font-size: 0.8rem;
  animation: blink 3s infinite;
}

.mouth {
  font-size: 1rem;
}

.character-stem {
  font-size: 1.5rem;
  margin-top: -4px;
}

@keyframes blink {
  0%, 90%, 100% { opacity: 1; }
  95% { opacity: 0.3; }
}

/* 状态效果 */
.status-effects {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: none;
}

.thinking-effect {
  position: relative;
}

.thinking-bubble {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 8px 12px;
  font-size: 1.2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: bubbleFloat 2s ease-in-out infinite;
}

.thinking-bubble::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid rgba(255, 255, 255, 0.9);
}

.speaking-effect {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.speaking-text {
  font-size: 0.7rem;
  color: #10b981;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
  animation: fadeInOut 2s ease-in-out infinite;
}

.sound-waves {
  display: flex;
  gap: 2px;
  align-items: center;
}

.sound-waves span {
  width: 3px;
  background: #4caf50;
  border-radius: 2px;
  animation: soundWave 0.5s infinite ease-in-out alternate;
}

.sound-waves span:nth-child(1) {
  height: 12px;
  animation-delay: 0s;
}

.sound-waves span:nth-child(2) {
  height: 18px;
  animation-delay: 0.1s;
}

.sound-waves span:nth-child(3) {
  height: 8px;
  animation-delay: 0.2s;
}

.listening-effect {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 2px solid #ff4444;
  border-radius: 50%;
  animation: pulseRing 1s infinite;
}

.pulse-ring::before {
  content: '🎤';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1rem;
}

/* 交互提示 */
.interaction-hint {
  position: absolute;
  top: -60px;
  right: 0;
  animation: bounceIn 1s ease-out;
}

.hint-bubble {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  white-space: nowrap;
  position: relative;
  animation: pulse 2s infinite;
}

.hint-arrow {
  position: absolute;
  bottom: -5px;
  right: 20px;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid rgba(0, 0, 0, 0.8);
}

/* AI对话界面 */
.ai-chat-interface {
  position: absolute;
  bottom: 120px;
  right: 0;
  width: 420px;
  height: 650px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 28px;
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.12),
    0 16px 32px rgba(0, 0, 0, 0.08),
    0 8px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
}

/* 聊天头部 */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.chat-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  pointer-events: none;
}

.character-info {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  z-index: 1;
}

.character-avatar-mini {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-radius: 50%;
  position: relative;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.character-avatar-mini img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.character-details h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.character-details .status {
  margin: 2px 0 0 0;
  font-size: 0.8rem;
  opacity: 0.9;
}

.chat-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.control-btn.voice-control {
  background: rgba(255, 255, 255, 0.15);
}

.control-btn.voice-control.active {
  background: #10b981;
  box-shadow: 0 0 15px rgba(16, 185, 129, 0.5);
}

.control-btn.voice-control:hover {
  background: rgba(16, 185, 129, 0.8);
}

.control-btn.voice-call-control {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: 2px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: visible; /* 改为visible以显示提示 */
}

.control-btn.voice-call-control::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.control-btn.voice-call-control:hover::before {
  left: 100%;
}

.control-btn.voice-call-control.active {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.6);
  animation: pulse 2s infinite;
}

.control-btn.voice-call-control:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

/* 欢迎消息样式 */
.welcome-message {
  padding: 20px;
  text-align: center;
}

.welcome-content {
  max-width: 300px;
  margin: 0 auto;
}

.ai-avatar {
  width: 80px;
  height: 80px;
  margin: 0 auto 15px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.ai-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.welcome-content h3 {
  color: #333;
  margin: 0 0 10px;
  font-size: 18px;
  font-weight: 600;
}

.welcome-content p {
  color: #666;
  margin: 0 0 20px;
  font-size: 14px;
  line-height: 1.5;
}

.welcome-tip {
  font-size: 12px !important;
  color: #999 !important;
  margin-top: 20px !important;
}

/* 语音通话区域样式 */
.voice-call-section {
  margin: 20px 0;
}

.voice-call-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  padding: 20px;
  color: white;
  text-align: left;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.voice-call-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
}

.voice-call-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.voice-call-info h4 {
  margin: 0 0 8px;
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.voice-call-info p {
  margin: 0 0 15px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

.voice-call-btn {
  width: 100%;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  backdrop-filter: blur(10px);
}

.voice-call-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.voice-call-btn.active {
  background: rgba(239, 68, 68, 0.8);
  border-color: rgba(239, 68, 68, 0.9);
  animation: pulse 2s infinite;
}

.voice-call-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.voice-call-btn .voice-call-icon {
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.voice-call-quick-btn .voice-call-icon {
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.control-btn .voice-call-icon {
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 16px;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.control-icon {
  font-size: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 语音通话提示 */
.voice-call-tip {
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: #ff6b6b;
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 11px;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
  animation: bounce 2s infinite;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 5px;
}

.voice-call-tip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: #ff6b6b;
}

.tip-close {
  background: none;
  border: none;
  color: white;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  margin-left: 5px;
  line-height: 1;
}

.tip-close:hover {
  opacity: 0.8;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-5px);
  }
  60% {
    transform: translateX(-50%) translateY(-3px);
  }
}

/* 聊天消息 */
.chat-messages {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  max-height: 300px;
  background: linear-gradient(to bottom, transparent 0%, rgba(248, 250, 252, 0.3) 100%);
  scroll-behavior: smooth;
}

.message {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;
  animation: messageSlideIn 0.4s ease-out;
}

.message.user {
  justify-content: flex-end;
}

.message-content {
  max-width: 85%;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 16px 20px;
  border-radius: 24px;
  border-top-left-radius: 8px;
  position: relative;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.08),
    0 2px 4px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.message-content::before {
  content: '';
  position: absolute;
  top: 12px;
  left: -8px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #ffffff;
  filter: drop-shadow(-1px 0 1px rgba(0, 0, 0, 0.05));
}

.message.user .message-content {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-top-left-radius: 24px;
  border-top-right-radius: 8px;
  box-shadow:
    0 4px 12px rgba(16, 185, 129, 0.3),
    0 2px 4px rgba(16, 185, 129, 0.2);
}

.message.user .message-content::before {
  left: auto;
  right: -8px;
  border-right: none;
  border-left: 8px solid #10b981;
  filter: drop-shadow(1px 0 1px rgba(0, 0, 0, 0.1));
}

.message-text {
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 4px;
}

.message-time {
  font-size: 0.7rem;
  color: #9e9e9e;
  text-align: right;
}

.message.user .message-time {
  color: rgba(255, 255, 255, 0.8);
}

.thinking-message .message-content {
  background: #e3f2fd;
  color: #1976d2;
}

.thinking-animation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-animation .dots {
  display: flex;
  gap: 2px;
}

.thinking-animation .dots span {
  width: 4px;
  height: 4px;
  background: #1976d2;
  border-radius: 50%;
  animation: thinking 1.4s infinite ease-in-out;
}

/* 快捷导航 */
.quick-navigation {
  padding: 16px 20px;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.quick-navigation h4 {
  margin: 0 0 12px 0;
  font-size: 0.9rem;
  color: #666;
}

.navigation-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
}

.nav-btn {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  background: white;
  border-radius: 12px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.nav-btn:hover {
  border-color: #4caf50;
  color: #4caf50;
  transform: translateY(-1px);
}

.nav-btn.current {
  background: #4caf50;
  color: white;
  border-color: #4caf50;
}

.nav-btn i {
  font-size: 1rem;
}

/* 智能建议 */
.smart-suggestions {
  padding: 16px 20px;
  border-top: 1px solid #e0e0e0;
  background: #fafafa;
}

.smart-suggestions h4 {
  margin: 0 0 12px 0;
  font-size: 0.9rem;
  color: #666;
}

.suggestions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.suggestion-btn {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  background: white;
  border-radius: 12px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.suggestion-btn:hover {
  border-color: #4caf50;
  color: #4caf50;
  transform: translateY(-1px);
}

/* 输入区域 */
.chat-input-area {
  padding: 20px;
  border-top: 1px solid #e0e0e0;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 12px;
}

.message-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 25px;
  font-size: 0.9rem;
  outline: none;
  transition: border-color 0.3s ease;
}

.message-input:focus {
  border-color: #4caf50;
}

.voice-btn,
.send-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-btn {
  background: #f5f5f5;
  color: #666;
}

.voice-btn.active {
  background: #ff4444;
  color: white;
  animation: pulse 1s infinite;
}

.voice-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 快捷语音通话按钮 */
.voice-call-quick-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.voice-call-quick-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.voice-call-quick-btn.active {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  animation: pulse 2s infinite;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* 已移除，使用emoji图标 */

.send-btn {
  background: #4caf50;
  color: white;
}

.send-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.send-btn:not(:disabled):hover {
  background: #388e3c;
  transform: scale(1.1);
}

/* 语音状态 */
.voice-status {
  display: flex;
  align-items: center;
  gap: 16px;
  color: #3b82f6;
  font-size: 14px;
  font-weight: 500;
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  margin-top: 12px;
  animation: slideUp 0.3s ease-out;
}

.voice-animation {
  position: relative;
  display: flex;
  align-items: center;
  gap: 4px;
}

.voice-circle {
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
  animation: voiceWave 1.2s infinite ease-in-out;
}

.voice-circle:nth-child(1) {
  animation-delay: 0s;
}

.voice-circle:nth-child(2) {
  animation-delay: 0.2s;
}

.voice-circle:nth-child(3) {
  animation-delay: 0.4s;
}

/* 动画定义 */
@keyframes bounceIn {
  0% { opacity: 0; transform: scale(0.3) translateY(20px); }
  50% { opacity: 1; transform: scale(1.05) translateY(-10px); }
  70% { transform: scale(0.9) translateY(0); }
  100% { transform: scale(1) translateY(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.05); }
}

@keyframes slideUp {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes voicePulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

@keyframes voiceWave {
  0%, 40%, 100% {
    transform: scaleY(0.4);
    opacity: 0.5;
  }
  20% {
    transform: scaleY(1);
    opacity: 1;
  }
}

@keyframes thinking {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

/* 2D角色动画 */
@keyframes characterFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

@keyframes characterThink {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(-3deg); }
  75% { transform: rotate(3deg); }
}

@keyframes characterSpeak {
  0% { transform: scale(1); }
  100% { transform: scale(1.05); }
}

@keyframes characterListen {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.08); }
}



@keyframes fadeInOut {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

@keyframes bubbleFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-4px); }
}

@keyframes soundWave {
  0% { transform: scaleY(1); }
  100% { transform: scaleY(1.5); }
}

@keyframes pulseRing {
  0% { transform: scale(0.8); opacity: 1; }
  100% { transform: scale(1.4); opacity: 0; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-chat-interface {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    animation: slideUp 0.3s ease-out;
  }

  .threejs-container.expanded {
    top: 20px;
    right: 20px;
  }

  .navigation-grid {
    grid-template-columns: 1fr 1fr;
  }

  .suggestions-grid {
    grid-template-columns: 1fr;
  }
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #059669, #047857);
}

/* 动画效果 */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 实时语音通话界面样式 */
.voice-call-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(10px);
}

.voice-call-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 30px;
  width: 400px;
  max-width: 90vw;
  color: white;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.voice-call-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.voice-call-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.close-call-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-call-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.call-avatar-section {
  margin-bottom: 30px;
}

.call-avatar-container {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 20px;
  border-radius: 50%;
  overflow: hidden;
  transition: all 0.3s ease;
}

.call-avatar-container.speaking {
  transform: scale(1.1);
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
}

.call-avatar-container.listening {
  animation: pulse 2s infinite;
}

.call-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.speaking-indicator {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 3px;
}

.sound-wave {
  width: 4px;
  height: 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  animation: soundWave 1s ease-in-out infinite;
}

.sound-wave:nth-child(1) { animation-delay: 0s; }
.sound-wave:nth-child(2) { animation-delay: 0.2s; }
.sound-wave:nth-child(3) { animation-delay: 0.4s; }

.listening-indicator {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: pulse-ring 2s ease-out infinite;
}

.call-status {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.call-controls {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 30px 0;
}

.call-control-btn {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.call-control-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.mic-btn {
  background: #10b981;
}

.mic-btn.active {
  background: #ef4444;
  animation: pulse 1.5s infinite;
}

.speaker-btn {
  background: #3b82f6;
}

.speaker-btn.active {
  background: #8b5cf6;
}

.end-call-btn {
  background: #ef4444;
}

.end-call-btn:hover {
  background: #dc2626;
}
</style>
