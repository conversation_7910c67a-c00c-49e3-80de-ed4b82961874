<template>
  <div class="page-container fade-in">
    <AppNavbar />
    <main class="main-content">
      <!-- 页面头部Banner -->
      <section class="hero-section">
        <div class="hero-background">
          <img src="https://images.unsplash.com/photo-1544787219-7f47ccb76574?auto=format&fit=crop&w=1920&h=800&q=80" alt="周边产品">
          <div class="hero-overlay"></div>
        </div>
        <div class="hero-content">
          <h1 class="hero-title">庄园周边产品</h1>
          <p class="hero-subtitle">精选云南特色产品，带走庄园的美好回忆</p>
          <div class="hero-buttons">
            <button @click="handleShopClick" class="btn btn-primary btn-large">立即选购</button>
            <button @click="handleLearnMoreClick" class="btn btn-outline-white btn-large">了解更多</button>
          </div>
        </div>
      </section>

      <!-- 产品分类导航 -->
      <section class="category-section">
        <div class="container">
          <div class="category-nav">
            <button 
              v-for="category in categories" 
              :key="category.id"
              @click="activeCategory = category.id"
              class="category-btn"
              :class="{ active: activeCategory === category.id }"
            >
              <i :class="category.icon"></i>
              {{ category.name }}
            </button>
          </div>
        </div>
      </section>

      <!-- 特色推荐产品 -->
      <section class="featured-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">特色推荐</h2>
            <p class="section-subtitle">庄园精选，品质保证</p>
          </div>
          
          <div class="featured-grid">
            <div 
              v-for="product in featuredProducts" 
              :key="product.id"
              class="featured-card slide-in-up"
              :style="{ animationDelay: product.delay }"
            >
              <div class="product-image">
                <img :src="product.image" :alt="product.name">
                <div class="product-badge" :class="product.badgeType">{{ product.badge }}</div>
              </div>
              <div class="product-content">
                <h3 class="product-title">{{ product.name }}</h3>
                <p class="product-desc">{{ product.description }}</p>
                <div class="product-footer">
                  <div class="product-price">
                    <span class="current-price">¥{{ product.price }}</span>
                    <span v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</span>
                  </div>
                  <button @click="handleAddToCartClick" class="product-btn">
                    <i class="fas fa-shopping-cart"></i>
                    加入购物车
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 产品展示区域 -->
      <section class="products-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">全部产品</h2>
            <p class="section-subtitle">发现更多庄园好物</p>
          </div>

          <div class="products-grid">
            <div 
              v-for="product in filteredProducts" 
              :key="product.id"
              class="product-card"
            >
              <div class="product-image" @click="showProductDetail(product)">
                <div class="image-gallery" v-if="product.gallery && product.gallery.length > 1">
                  <img
                    v-for="(img, index) in product.gallery"
                    :key="index"
                    :src="img"
                    :alt="product.name"
                    :class="{ active: index === 0 }"
                    @mouseenter="switchImage($event, product.id)"
                  />
                </div>
                <img v-else :src="product.image" :alt="product.name" class="single-image" />
                <div v-if="product.badge" class="product-badge" :class="product.badgeType">{{ product.badge }}</div>
                <div v-if="product.gallery && product.gallery.length > 1" class="gallery-indicator">
                  <span v-for="(img, index) in product.gallery" :key="index" class="dot" :class="{ active: index === 0 }"></span>
                </div>
                <div class="product-overlay">
                  <button @click.stop="showProductDetail(product)" class="quick-view-btn">
                    <i class="fas fa-eye"></i>
                    查看详情
                  </button>
                </div>
              </div>
              <div class="product-info">
                <h4 class="product-name">{{ product.name }}</h4>
                <p class="product-description">{{ product.description }}</p>
                <div class="product-rating">
                  <div class="stars">
                    <i v-for="i in 5" :key="i" class="fas fa-star" :class="{ active: i <= product.rating }"></i>
                  </div>
                  <span class="rating-text">({{ product.reviews }})</span>
                </div>
                <div class="product-price-row">
                  <div class="price-info">
                    <span class="current-price">¥{{ product.price }}</span>
                    <span v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</span>
                  </div>
                  <button @click="handleAddToCartClick" class="add-cart-btn">
                    <i class="fas fa-shopping-cart"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="section-footer">
            <button @click="handleViewMoreClick" class="btn btn-outline btn-large">查看更多产品</button>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3 class="footer-logo">普洱蘑菇庄园</h3>
            <p class="footer-desc">精选云南特色产品，传承自然生态理念</p>
            <div class="social-links">
              <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
              <a href="#" class="social-link"><i class="fab fa-weibo"></i></a>
              <a href="#" class="social-link"><i class="fab fa-wechat"></i></a>
            </div>
          </div>
          <div class="footer-section">
            <h4 class="footer-title">产品分类</h4>
            <ul class="footer-links">
              <li><a href="#" class="footer-link">普洱茶系列</a></li>
              <li><a href="#" class="footer-link">蘑菇制品</a></li>
              <li><a href="#" class="footer-link">手工艺品</a></li>
              <li><a href="#" class="footer-link">生态食品</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4 class="footer-title">购买服务</h4>
            <ul class="footer-links">
              <li><a href="#" class="footer-link">在线购买</a></li>
              <li><a href="#" class="footer-link">配送说明</a></li>
              <li><a href="#" class="footer-link">退换政策</a></li>
              <li><a href="#" class="footer-link">客户服务</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4 class="footer-title">联系我们</h4>
            <div class="contact-info">
              <div class="contact-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>云南省普洱市</span>
              </div>
              <div class="contact-item">
                <i class="fas fa-phone"></i>
                <span>************</span>
              </div>
              <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2025 普洱蘑菇庄园民宿. 保留所有权利.</p>
        </div>
      </div>
    </footer>

    <!-- 产品详情弹窗 -->
    <div v-if="showDetail" class="product-detail-modal" @click="closeDetail">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ selectedProduct?.name }}</h3>
          <button @click="closeDetail" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="detail-image">
            <div class="image-gallery" v-if="selectedProduct?.gallery && selectedProduct.gallery.length > 1">
              <img
                v-for="(img, index) in selectedProduct.gallery"
                :key="index"
                :src="img"
                :alt="selectedProduct.name"
                :class="{ active: index === 0 }"
                @click="switchDetailImage($event)"
              />
            </div>
            <img v-else :src="selectedProduct?.image" :alt="selectedProduct?.name" />
          </div>
          <div class="detail-info">
            <p class="detail-desc">{{ selectedProduct?.fullDescription || selectedProduct?.description }}</p>
            <div class="detail-specs" v-if="selectedProduct?.specs">
              <h4>产品规格</h4>
              <ul>
                <li v-for="spec in selectedProduct.specs" :key="spec">{{ spec }}</li>
              </ul>
            </div>
            <div class="detail-rating">
              <div class="stars">
                <i v-for="i in 5" :key="i" class="fas fa-star" :class="{ active: i <= (selectedProduct?.rating || 0) }"></i>
              </div>
              <span class="rating-text">({{ selectedProduct?.reviews || 0 }}条评价)</span>
            </div>
            <div class="detail-price">
              <span class="current-price">¥{{ selectedProduct?.price }}</span>
              <span v-if="selectedProduct?.originalPrice" class="original-price">¥{{ selectedProduct?.originalPrice }}</span>
            </div>
            <div class="detail-actions">
              <button class="btn-primary btn-large">立即购买</button>
              <button class="btn-secondary btn-large">加入购物车</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import AppNavbar from '../components/AppNavbar.vue'

const router = useRouter()
const auth = useAuthStore()

// 产品分类
const categories = ref([
  { id: 'all', name: '全部', icon: 'fas fa-th-large' },
  { id: 'tea', name: '茶叶产品', icon: 'fas fa-leaf' },
  { id: 'ip', name: 'IP衍生品', icon: 'fas fa-star' },
  { id: 'souvenir', name: '纪念品', icon: 'fas fa-gift' }
])

const activeCategory = ref('all')

// 响应式数据
const showDetail = ref(false)
const selectedProduct = ref(null)

// 特色推荐产品
const featuredProducts = ref([
  {
    id: 1,
    name: '精品普洱茶饼',
    description: '选用优质大叶种茶叶，传统工艺制作，茶汤红浓明亮，滋味醇厚回甘',
    image: '/src/assets/images/产品/茶饼/茶饼包装.jpg',
    gallery: [
      '/src/assets/images/产品/茶饼/茶饼包装.jpg',
      '/src/assets/images/产品/茶饼/茶饼原料1.jpg',
      '/src/assets/images/产品/茶饼/茶饼原料2.jpg'
    ],
    price: 168,
    originalPrice: 198,
    badge: '热销',
    badgeType: 'hot',
    delay: '0.1s',
    category: 'tea',
    specs: ['重量：357g', '年份：2023年', '产地：云南普洱', '包装：棉纸包装']
  },
  {
    id: 2,
    name: '茶茶主题帆布包',
    description: '以茶茶为主题设计的帆布包，采用优质帆布材料，结实耐用，既实用又具有纪念意义',
    image: '/src/assets/images/IP形象衍生产品/帆布包/帆布包1.jpg',
    gallery: [
      '/src/assets/images/IP形象衍生产品/帆布包/帆布包1.jpg',
      '/src/assets/images/IP形象衍生产品/帆布包/帆布包2.png',
      '/src/assets/images/IP形象衍生产品/帆布包/帆布包3.jpg'
    ],
    price: 58,
    badge: '新品',
    badgeType: 'new',
    delay: '0.2s',
    category: 'ip',
    specs: ['材质：优质帆布', '尺寸：35×40×12cm', '设计：茶茶主题', '容量：15L']
  },
  {
    id: 3,
    name: '庄园纪念勋章（金色版）',
    description: '普洱蘑菇庄园专属纪念勋章，采用精美工艺制作，具有很高的收藏和纪念价值',
    image: '/src/assets/images/IP形象衍生产品/勋章/勋章1.jpg',
    gallery: [
      '/src/assets/images/IP形象衍生产品/勋章/勋章1.jpg',
      '/src/assets/images/IP形象衍生产品/勋章/勋章2.jpg'
    ],
    price: 38,
    badge: '限量',
    badgeType: 'recommend',
    delay: '0.3s',
    category: 'souvenir',
    specs: ['材质：合金镀金', '直径：4cm', '设计：庄园专属', '限量：1000枚']
  }
])

// 茶叶产品数据
const teaProducts = ref([
  {
    id: 1,
    name: '精品普洱茶饼',
    description: '选用优质大叶种茶叶，传统工艺制作',
    fullDescription: '这款精品普洱茶饼选用云南大叶种茶叶，经过传统工艺精心制作。茶汤红浓明亮，滋味醇厚回甘，具有独特的陈香。适合收藏和日常品饮。',
    image: '/src/assets/images/产品/茶饼/茶饼包装.jpg',
    gallery: [
      '/src/assets/images/产品/茶饼/茶饼包装.jpg',
      '/src/assets/images/产品/茶饼/茶饼原料1.jpg',
      '/src/assets/images/产品/茶饼/茶饼原料2.jpg',
      '/src/assets/images/产品/茶饼/茶饼原料3.jpg'
    ],
    price: 168,
    originalPrice: 198,
    badge: '热销',
    category: 'tea',
    categoryId: 'tea',
    specs: ['重量：357g', '年份：2023年', '产地：云南普洱', '包装：棉纸包装'],
    rating: 5,
    reviews: 89
  },
  {
    id: 2,
    name: '散装普洱茶（一级）',
    description: '新鲜采摘，自然晒干，原汁原味',
    fullDescription: '采用当季新鲜茶叶，经过自然晒干工艺，保持茶叶的原始香味。茶汤清澈，口感清香甘甜，是日常品茶的绝佳选择。',
    image: '/src/assets/images/产品/普洱茶/普洱茶1.jpg',
    gallery: [
      '/src/assets/images/产品/普洱茶/普洱茶1.jpg',
      '/src/assets/images/产品/普洱茶/普洱茶2.jpg',
      '/src/assets/images/产品/普洱茶/普洱茶3.jpg'
    ],
    price: 88,
    originalPrice: 108,
    badge: '新品',
    category: 'tea',
    categoryId: 'tea',
    specs: ['重量：250g', '等级：一级', '产地：云南普洱', '包装：密封袋装'],
    rating: 4,
    reviews: 156
  },
  {
    id: 3,
    name: '普洱茶精装礼盒',
    description: '精美包装，送礼佳品',
    fullDescription: '精选多款优质普洱茶，配以精美礼盒包装。包含生茶、熟茶各一饼，适合商务送礼或节日馈赠。',
    image: '/src/assets/images/产品/普洱茶/普洱茶包装刀版图.jpg',
    gallery: [
      '/src/assets/images/产品/普洱茶/普洱茶包装刀版图.jpg',
      '/src/assets/images/产品/普洱茶/普洱茶摆放环境展示.jpg'
    ],
    price: 298,
    originalPrice: 358,
    badge: '礼品',
    category: 'tea',
    categoryId: 'tea',
    specs: ['内含：生茶357g + 熟茶357g', '包装：高档礼盒', '产地：云南普洱', '适合：送礼收藏'],
    rating: 5,
    reviews: 67
  },
  {
    id: 4,
    name: '陈年普洱茶饼（5年陈）',
    description: '5年自然陈化，口感醇厚',
    fullDescription: '经过5年自然陈化的普洱茶饼，茶性温和，口感醇厚甘甜。陈香浓郁，汤色红亮，是普洱茶爱好者的珍藏佳品。',
    image: '/src/assets/images/产品/茶饼/茶饼原料4.jpg',
    gallery: [
      '/src/assets/images/产品/茶饼/茶饼原料4.jpg',
      '/src/assets/images/产品/茶饼/茶饼原料1.jpg'
    ],
    price: 388,
    originalPrice: 458,
    badge: '珍藏',
    category: 'tea',
    categoryId: 'tea',
    specs: ['重量：357g', '年份：2019年', '陈化：5年', '产地：云南普洱'],
    rating: 5,
    reviews: 234
  }
])

// IP衍生产品数据
const ipProducts = ref([
  {
    id: 11,
    name: '茶茶主题帆布包',
    description: '可爱IP形象，实用环保',
    fullDescription: '以茶茶为主题设计的帆布包，采用优质帆布材料，结实耐用。包包印有可爱的茶茶形象，既实用又具有纪念意义。',
    image: '/src/assets/images/IP形象衍生产品/帆布包/帆布包1.jpg',
    gallery: [
      '/src/assets/images/IP形象衍生产品/帆布包/帆布包1.jpg',
      '/src/assets/images/IP形象衍生产品/帆布包/帆布包2.png',
      '/src/assets/images/IP形象衍生产品/帆布包/帆布包3.jpg'
    ],
    price: 58,
    badge: '热销',
    category: 'IP衍生品',
    categoryId: 'ip',
    specs: ['材质：优质帆布', '尺寸：35×40×12cm', '设计：茶茶主题', '容量：15L'],
    rating: 5,
    reviews: 123
  },
  {
    id: 12,
    name: '茶茶纪念挂坠',
    description: '精美挂坠，随身携带',
    fullDescription: '精致的茶茶主题挂坠，采用优质材料制作。可作为钥匙扣、包包挂饰使用，让茶茶时刻陪伴在您身边。',
    image: '/src/assets/images/IP形象衍生产品/挂坠/挂坠1.jpg',
    gallery: [
      '/src/assets/images/IP形象衍生产品/挂坠/挂坠1.jpg',
      '/src/assets/images/IP形象衍生产品/挂坠/挂坠2.jpg',
      '/src/assets/images/IP形象衍生产品/挂坠/挂坠展示.jpg'
    ],
    price: 28,
    badge: '新品',
    category: 'IP衍生品',
    categoryId: 'ip',
    specs: ['材质：合金+亚克力', '尺寸：5×3×0.5cm', '设计：茶茶形象', '用途：装饰挂件'],
    rating: 4,
    reviews: 89
  },
  {
    id: 13,
    name: '茶茶主题服装（女款）',
    description: '舒适面料，时尚设计',
    fullDescription: '以茶茶为主题设计的休闲服装，采用舒适透气的面料。印有可爱的茶茶图案，适合日常穿着和旅游纪念。',
    image: '/src/assets/images/IP形象衍生产品/衣服/衣服1（女）.jpg',
    gallery: [
      '/src/assets/images/IP形象衍生产品/衣服/衣服1（女）.jpg',
      '/src/assets/images/IP形象衍生产品/衣服/衣服3.png'
    ],
    price: 128,
    originalPrice: 158,
    badge: '限量',
    category: 'IP衍生品',
    categoryId: 'ip',
    specs: ['材质：纯棉', '尺寸：S/M/L/XL', '设计：茶茶主题', '颜色：白色/粉色'],
    rating: 5,
    reviews: 67
  },
  {
    id: 14,
    name: '茶茶定制水杯',
    description: '保温效果好，设计精美',
    fullDescription: '茶茶主题定制保温杯，采用优质不锈钢内胆，保温效果佳。杯身印有茶茶可爱形象，是日常使用和收藏的好选择。',
    image: '/src/assets/images/IP形象衍生产品/水杯/水杯1.jpg',
    price: 88,
    originalPrice: 108,
    badge: '实用',
    category: 'IP衍生品',
    categoryId: 'ip',
    specs: ['容量：500ml', '材质：304不锈钢', '保温：6-8小时', '设计：茶茶主题'],
    rating: 4,
    reviews: 156
  },
  {
    id: 15,
    name: '茶茶平板保护套',
    description: '茶茶图案，保护设备',
    fullDescription: '茶茶主题平板电脑保护套，采用优质PU皮革制作。内部有柔软绒布保护，外观印有精美的茶茶图案。',
    image: '/src/assets/images/IP形象衍生产品/平板/平板外壳1.png',
    gallery: [
      '/src/assets/images/IP形象衍生产品/平板/平板外壳1.png',
      '/src/assets/images/IP形象衍生产品/平板/平板外壳2.png',
      '/src/assets/images/IP形象衍生产品/平板/平板外壳3.png'
    ],
    price: 68,
    badge: '新品',
    category: 'IP衍生品',
    categoryId: 'ip',
    specs: ['适用：9.7-10.1寸平板', '材质：PU皮革+绒布', '设计：茶茶主题', '功能：防摔保护'],
    rating: 5,
    reviews: 234
  }
])

// 纪念品数据
const souvenirProducts = ref([
  {
    id: 21,
    name: '庄园纪念勋章（金色版）',
    description: '独特设计，收藏价值',
    fullDescription: '普洱蘑菇庄园专属纪念勋章，采用精美工艺制作。勋章图案融合了茶叶和蘑菇元素，具有很高的收藏和纪念价值。',
    image: '/src/assets/images/IP形象衍生产品/勋章/勋章1.jpg',
    gallery: [
      '/src/assets/images/IP形象衍生产品/勋章/勋章1.jpg',
      '/src/assets/images/IP形象衍生产品/勋章/勋章2.jpg'
    ],
    price: 38,
    badge: '限量',
    category: '纪念品',
    categoryId: 'souvenir',
    specs: ['材质：合金镀金', '直径：4cm', '设计：庄园专属', '限量：1000枚'],
    rating: 5,
    reviews: 89
  },
  {
    id: 22,
    name: '庄园纪念勋章（银色版）',
    description: '经典设计，典雅大方',
    fullDescription: '普洱蘑菇庄园纪念勋章银色版，设计典雅大方。采用优质合金材料，表面镀银处理，光泽持久。',
    image: '/src/assets/images/IP形象衍生产品/勋章/勋章2.jpg',
    gallery: [
      '/src/assets/images/IP形象衍生产品/勋章/勋章2.jpg',
      '/src/assets/images/IP形象衍生产品/勋章/勋章3.jpg'
    ],
    price: 28,
    badge: '经典',
    category: '纪念品',
    categoryId: 'souvenir',
    specs: ['材质：合金镀银', '直径：4cm', '设计：庄园专属', '工艺：压铸成型'],
    rating: 4,
    reviews: 123
  },
  {
    id: 23,
    name: '庄园纪念勋章（铜色版）',
    description: '复古风格，怀旧情怀',
    fullDescription: '普洱蘑菇庄园纪念勋章铜色版，复古风格设计，充满怀旧情怀。表面做旧处理，展现历史沧桑感。',
    image: '/src/assets/images/IP形象衍生产品/勋章/勋章3.jpg',
    gallery: [
      '/src/assets/images/IP形象衍生产品/勋章/勋章3.jpg',
      '/src/assets/images/IP形象衍生产品/勋章/勋章1.jpg'
    ],
    price: 25,
    badge: '复古',
    category: '纪念品',
    categoryId: 'souvenir',
    specs: ['材质：合金做旧', '直径：4cm', '设计：庄园专属', '工艺：仿古处理'],
    rating: 4,
    reviews: 67
  }
])

// 合并所有产品
const allProducts = computed(() => {
  return [...teaProducts.value, ...ipProducts.value, ...souvenirProducts.value]
})

// 过滤产品
const filteredProducts = computed(() => {
  if (activeCategory.value === 'all') {
    return allProducts.value
  }
  return allProducts.value.filter((product: any) => product.categoryId === activeCategory.value)
})

// 显示产品详情
const showProductDetail = (product: any) => {
  selectedProduct.value = product
  showDetail.value = true
}

// 关闭产品详情
const closeDetail = () => {
  showDetail.value = false
  selectedProduct.value = null
}

// 图片切换功能
const switchImage = (event: Event, productId: number) => {
  const target = event.target as HTMLImageElement
  const container = target.closest('.product-image')
  if (container) {
    // 移除所有active类
    const images = container.querySelectorAll('.image-gallery img')
    const dots = container.querySelectorAll('.gallery-indicator .dot')

    images.forEach((img, index) => {
      img.classList.remove('active')
      if (dots[index]) {
        dots[index].classList.remove('active')
      }
    })

    // 添加active类到当前图片
    target.classList.add('active')
    const imageIndex = Array.from(images).indexOf(target)
    if (dots[imageIndex]) {
      dots[imageIndex].classList.add('active')
    }
  }
}

// 详情图片切换功能
const switchDetailImage = (event: Event) => {
  const target = event.target as HTMLImageElement
  const container = target.closest('.image-gallery')
  if (container) {
    const images = container.querySelectorAll('img')
    images.forEach(img => img.classList.remove('active'))
    target.classList.add('active')
  }
}

// 登录检查函数
const requireLogin = () => {
  if (!auth.isAuthenticated) {
    router.push('/login')
    return false
  }
  return true
}

// 处理立即选购点击
const handleShopClick = () => {
  if (!requireLogin()) return
  // 已登录，可以进行购物操作
  alert('购物功能开发中...')
}

// 处理了解更多点击
const handleLearnMoreClick = () => {
  // 了解更多不需要登录
  alert('了解更多功能开发中...')
}

// 处理加入购物车点击
const handleAddToCartClick = () => {
  if (!requireLogin()) return
  // 已登录，可以加入购物车
  alert('加入购物车功能开发中...')
}

// 处理快速查看点击
const handleQuickViewClick = () => {
  if (!requireLogin()) return
  // 已登录，可以查看详情
  alert('快速查看功能开发中...')
}

// 处理查看更多点击
const handleViewMoreClick = () => {
  if (!requireLogin()) return
  // 已登录，可以查看更多产品
  alert('查看更多产品功能开发中...')
}
</script>

<style scoped>
/* 继承HomeView的基础样式 */
.page-container {
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 英雄区域 */
.hero-section {
  position: relative;
  height: 500px;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  width: 100%;
  height: 100%;
}

.hero-background img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  z-index: 10;
  width: 90%;
  max-width: 600px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 24px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 32px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.hero-buttons {
  display: flex;
  gap: 24px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 按钮样式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background-color: #d4af37;
  color: white;
}

.btn-primary:hover {
  background-color: #b8941f;
}

.btn-outline {
  background-color: transparent;
  color: #d4af37;
  border: 2px solid #d4af37;
}

.btn-outline:hover {
  background-color: #d4af37;
  color: white;
}

.btn-outline-white {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.btn-outline-white:hover {
  background-color: white;
  color: #d4af37;
}

.btn-large {
  padding: 16px 32px;
  font-size: 18px;
}

/* 分类导航 */
.category-section {
  padding: 40px 0;
  background-color: #f9fafb;
}

.category-nav {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.category-btn {
  padding: 12px 24px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  color: #6b7280;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-btn:hover,
.category-btn.active {
  background: #d4af37;
  border-color: #d4af37;
  color: white;
  transform: translateY(-2px);
}

/* 通用区域样式 */
.featured-section,
.products-section {
  padding: 80px 0;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 3rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 1.25rem;
  color: #6b7280;
}

/* 特色产品网格 */
.featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
  margin-bottom: 40px;
}

.featured-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.featured-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.product-image {
  position: relative;
  height: 240px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.featured-card:hover .product-image img {
  transform: scale(1.05);
}

.product-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
}

.product-badge.hot {
  background-color: #d4af37;
}

.product-badge.new {
  background-color: #10b981;
}

.product-badge.recommend {
  background-color: #f59e0b;
}

.product-content {
  padding: 24px;
}

.product-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 12px;
}

.product-desc {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 20px;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 1.5rem;
  font-weight: bold;
  color: #d4af37;
}

.original-price {
  font-size: 1rem;
  color: #9ca3af;
  text-decoration: line-through;
}

.product-btn {
  background: #d4af37;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.product-btn:hover {
  background: #b8941f;
  transform: translateY(-1px);
}

/* 产品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.product-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.product-card .product-image {
  position: relative;
  height: 200px;
}

.product-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.quick-view-btn {
  background: white;
  color: #1f2937;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.quick-view-btn:hover {
  background: #d4af37;
  color: white;
}

.product-info {
  padding: 16px;
}

.product-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.product-category {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 8px;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.stars {
  display: flex;
  gap: 2px;
}

.stars i {
  color: #e5e7eb;
  font-size: 0.875rem;
}

.stars i.active {
  color: #d4af37;
}

.rating-text {
  color: #6b7280;
  font-size: 0.875rem;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 1.25rem;
  font-weight: bold;
  color: #d4af37;
}

.add-cart-btn {
  width: 32px;
  height: 32px;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-cart-btn:hover {
  background: #b8941f;
  transform: scale(1.1);
}

.section-footer {
  text-align: center;
}

/* 页脚样式 */
.footer {
  background-color: #1f2937;
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-logo {
  font-size: 1.75rem;
  font-weight: bold;
  color: #d4af37;
  margin-bottom: 16px;
  font-family: 'Pacifico', cursive;
}

.footer-desc {
  color: #d1d5db;
  line-height: 1.6;
  margin-bottom: 24px;
}

.social-links {
  display: flex;
  gap: 16px;
}

.social-link {
  color: #d1d5db;
  font-size: 1.25rem;
  transition: color 0.3s ease;
}

.social-link:hover {
  color: #d4af37;
}

.footer-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-link {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #d4af37;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #d1d5db;
}

.contact-item i {
  color: #d4af37;
  width: 20px;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding-top: 24px;
  text-align: center;
  color: #9ca3af;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.8s ease-out;
}

.slide-in-up {
  animation: slideInUp 0.8s ease-out;
  animation-fill-mode: both;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .featured-grid,
  .products-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .category-nav {
    gap: 12px;
  }

  .category-btn {
    padding: 10px 16px;
    font-size: 14px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 32px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .featured-grid,
  .products-grid {
    gap: 16px;
  }
}

/* 图片轮播样式 */
.image-gallery {
  position: relative;
  width: 100%;
  height: 100%;
}

.image-gallery img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
  cursor: pointer;
}

.image-gallery img.active {
  opacity: 1;
}

.image-gallery img:hover {
  transform: scale(1.05);
}

.single-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .single-image {
  transform: scale(1.05);
}

.gallery-indicator {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 5px;
}

.gallery-indicator .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.gallery-indicator .dot.active {
  background: white;
}

.product-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
  z-index: 2;
}

.product-badge.hot {
  background: #ff6b6b;
}

.product-badge.new {
  background: #4ecdc4;
}

.product-badge.recommend {
  background: #45b7d1;
}

/* 价格显示优化 */
.price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.current-price {
  font-size: 1.2rem;
  font-weight: 600;
  color: #e74c3c;
}

.original-price {
  font-size: 0.9rem;
  text-decoration: line-through;
  color: #999;
  margin-top: 2px;
}

/* 产品详情弹窗 */
.product-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 20px;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  margin: 20px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  padding: 30px;
}

.detail-image {
  position: relative;
}

.detail-image .image-gallery {
  height: 300px;
  border-radius: 10px;
  overflow: hidden;
}

.detail-image .image-gallery img {
  cursor: pointer;
  border-radius: 10px;
}

.detail-image img:not(.image-gallery img) {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 10px;
}

.detail-desc {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.detail-specs h4 {
  margin-bottom: 10px;
  color: #333;
}

.detail-specs ul {
  list-style: none;
  padding: 0;
  margin-bottom: 20px;
}

.detail-specs li {
  padding: 5px 0;
  color: #666;
  border-bottom: 1px solid #f0f0f0;
}

.detail-rating {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.detail-rating .stars {
  display: flex;
  gap: 2px;
}

.detail-rating .stars i {
  color: #ddd;
  font-size: 14px;
}

.detail-rating .stars i.active {
  color: #ffc107;
}

.detail-rating .rating-text {
  color: #666;
  font-size: 14px;
}

.detail-price {
  margin-bottom: 30px;
}

.detail-price .current-price {
  font-size: 1.8rem;
  font-weight: 600;
  color: #e74c3c;
}

.detail-price .original-price {
  margin-left: 10px;
  font-size: 1.2rem;
  text-decoration: line-through;
  color: #999;
}

.detail-actions {
  display: flex;
  gap: 15px;
}

.btn-large {
  padding: 15px 30px;
  font-size: 1.1rem;
}

.btn-primary {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: #e9ecef;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-body {
    grid-template-columns: 1fr;
  }

  .detail-actions {
    flex-direction: column;
  }
}
</style>
